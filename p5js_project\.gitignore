# Frontend Project .gitignore
# This file lists files and directories that should be ignored by Git

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build outputs
dist/
build/
out/
.bundle/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
*.tmp

# Media files that might be generated
*.min.js
*.min.css

# Cache
.cache/
.parcel-cache/

# Miscellaneous
*.zip
*.rar
*.7z

# Editor directories and files
.atom/
.emacs.desktop
.emacs.desktop.lock
.project
.settings/
.vimrc
.vim/
.history/

# VIM
[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
*.un~
Session.vim
.netrwhist
*~

# Mac files
.fseventsd
.Icon\r
.DocumentRevisions-V100
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows files
$RECYCLE.BIN/
*.lnk
*.cab
*.msi
*.msm
*.msp
*.log