// Enhanced Heart Fireworks Game - Complete Implementation

// ============= UTILITY FUNCTIONS =============

// 辅助函数：绘制星形
function star(x, y, radius1, radius2, npoints) {
  let angle = TWO_PI / npoints;
  let halfAngle = angle / 2.0;
  beginShape();
  for (let a = 0; a < TWO_PI; a += angle) {
    let sx = x + cos(a) * radius2;
    let sy = y + sin(a) * radius2;
    vertex(sx, sy);
    sx = x + cos(a + halfAngle) * radius1;
    sy = y + sin(a + halfAngle) * radius1;
    vertex(sx, sy);
  }
  endShape(CLOSE);
}

// 缓动函数
function easeOutQuart(t) {
  return 1 - pow(1 - t, 4);
}

function easeInOutCubic(t) {
  return t < 0.5 ? 4 * t * t * t : 1 - pow(-2 * t + 2, 3) / 2;
}

// 颜色插值
function lerpColorCustom(c1, c2, amt) {
  return color(
    lerp(red(c1), red(c2), amt),
    lerp(green(c1), green(c2), amt),
    lerp(blue(c1), blue(c2), amt),
    lerp(alpha(c1), alpha(c2), amt)
  );
}

// ============= ENHANCED RESPONSIVE UI SYSTEM =============

// 响应式UI配置
let responsiveUI = {
  scale: 1,
  breakpoints: {
    mobile: 600,
    tablet: 900,
    desktop: 1200
  },
  currentBreakpoint: 'desktop',
  uiElements: {
    fontSize: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 18
    },
    spacing: {
      small: 10,
      medium: 15,
      large: 20
    },
    panelSizes: {
      mobile: { width: 0.9, height: 0.15 },
      tablet: { width: 300, height: 180 },
      desktop: { width: 350, height: 200 }
    }
  }
};

// 移动设备检测和适配
let isMobile = false;
let touchControls = false;
let devicePixelRatio = 1;

function detectMobile() {
  isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  touchControls = isMobile || 'ontouchstart' in window;
  devicePixelRatio = window.devicePixelRatio || 1;

  // 初始化响应式UI
  updateResponsiveUI();

  if (isMobile) {
    // 移动设备优化设置
    currentPerformanceLevel = "MEDIUM";

    // 延迟显示Toast，确保所有依赖都已初始化
    setTimeout(() => {
      if (typeof showToast === 'function') {
        showToast("📱 移动设备已优化", "mobile");
      }
    }, 500);

    // 调整UI缩放
    if (width < 600) {
      // 小屏幕设备进一步优化
      currentPerformanceLevel = "LOW";
    }
  }
}

// 简化的响应式UI更新（修复性能问题）
function updateResponsiveUI() {
  // 简化断点判断，避免复杂计算
  if (width <= 600) {
    responsiveUI.currentBreakpoint = 'mobile';
    responsiveUI.scale = 0.8;
  } else if (width <= 900) {
    responsiveUI.currentBreakpoint = 'tablet';
    responsiveUI.scale = 1.0;
  } else {
    responsiveUI.currentBreakpoint = 'desktop';
    responsiveUI.scale = 1.2;
  }

  // 简化UI元素尺寸更新
  responsiveUI.uiElements.fontSize.medium = 14 * responsiveUI.scale;
  responsiveUI.uiElements.spacing.medium = 15 * responsiveUI.scale;
}

// 更新UI元素尺寸
function updateUIElementSizes() {
  let bp = responsiveUI.currentBreakpoint;
  let scale = responsiveUI.scale;

  // 动态调整字体大小
  responsiveUI.uiElements.fontSize = {
    small: Math.max(10, 12 * scale),
    medium: Math.max(12, 14 * scale),
    large: Math.max(14, 16 * scale),
    xlarge: Math.max(16, 18 * scale)
  };

  // 动态调整间距
  responsiveUI.uiElements.spacing = {
    small: Math.max(8, 10 * scale),
    medium: Math.max(12, 15 * scale),
    large: Math.max(16, 20 * scale)
  };
}

// 获取响应式字体大小
function getResponsiveFontSize(size = 'medium') {
  return responsiveUI.uiElements.fontSize[size] || 14;
}

// 获取响应式间距
function getResponsiveSpacing(size = 'medium') {
  return responsiveUI.uiElements.spacing[size] || 15;
}

// ============= ENHANCED TOUCH SYSTEM =============

// 触摸反馈系统
let touchFeedback = {
  ripples: [],
  vibrationEnabled: true,
  touchAreaMultiplier: 1.5, // 触摸区域放大倍数
  longPressThreshold: 500, // 长按阈值（毫秒）
  doubleTapThreshold: 300 // 双击阈值（毫秒）
};

// 触摸涟漪效果类
class TouchRipple {
  constructor(x, y) {
    this.x = x;
    this.y = y;
    this.radius = 0;
    this.maxRadius = 80;
    this.alpha = 255;
    this.lifetime = 30; // 0.5秒
    this.color = color(255, 255, 255);
  }

  update() {
    this.lifetime--;
    this.radius = lerp(this.radius, this.maxRadius, 0.2);
    this.alpha = map(this.lifetime, 0, 30, 0, 255);
  }

  show() {
    push();
    noFill();
    stroke(red(this.color), green(this.color), blue(this.color), this.alpha);
    strokeWeight(3);
    ellipse(this.x, this.y, this.radius * 2);

    // 内圈
    stroke(red(this.color), green(this.color), blue(this.color), this.alpha * 0.5);
    strokeWeight(1);
    ellipse(this.x, this.y, this.radius);
    pop();
  }

  isFinished() {
    return this.lifetime <= 0;
  }
}

// 触摸事件处理 - 改进版（不修改全局变量）
function touchStarted() {
  if (touchControls && gameState === "playing") {
    // 记录触摸开始时的数量
    touchStartCount = touches ? touches.length : 0;

    // 安全获取触摸位置，添加完整的安全检查
    let touchX = mouseX; // 默认值
    let touchY = mouseY; // 默认值

    if (touches && touches.length > 0 && touches[0]) {
      // 确保touches[0]存在且有x,y属性
      if (typeof touches[0].x === 'number' && typeof touches[0].y === 'number') {
        touchX = touches[0].x;
        touchY = touches[0].y;
      }
    }

    // 简化的触摸反馈 - 只保留基本震动
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }

    // 直接传递触摸坐标，不修改全局变量
    handleMouseClick(touchX, touchY);

    return false; // 防止默认行为
  }
}

// 添加触摸涟漪效果
function addTouchRipple(x, y, color = null) {
  let ripple = new TouchRipple(x, y);
  if (color) ripple.color = color;
  touchFeedback.ripples.push(ripple);

  // 限制涟漪数量
  if (touchFeedback.ripples.length > 5) {
    touchFeedback.ripples.shift();
  }
}

// 触摸震动反馈
function triggerTouchVibration(type = 'light') {
  if (!touchFeedback.vibrationEnabled || !navigator.vibrate) return;

  switch(type) {
    case 'light':
      navigator.vibrate(10);
      break;
    case 'medium':
      navigator.vibrate(25);
      break;
    case 'heavy':
      navigator.vibrate([50, 10, 50]);
      break;
    case 'success':
      navigator.vibrate([20, 10, 20, 10, 20]);
      break;
    case 'error':
      navigator.vibrate([100, 50, 100]);
      break;
  }
}

// 更新和绘制触摸反馈
function updateTouchFeedback() {
  // 更新涟漪效果
  for (let i = touchFeedback.ripples.length - 1; i >= 0; i--) {
    touchFeedback.ripples[i].update();
    touchFeedback.ripples[i].show();

    if (touchFeedback.ripples[i].isFinished()) {
      touchFeedback.ripples.splice(i, 1);
    }
  }
}

// 双指操作检测
let lastTouchTime = 0;
let touchCount = 0;
let touchStartCount = 0; // 记录触摸开始时的数量

// ============= ENHANCED TUTORIAL & USER EXPERIENCE SYSTEM =============

// 增强的新手引导系统
let tutorialStep = 0;
let tutorialActive = true;
let tutorialTimer = 0;
let tutorialHighlight = null;
let practiceMode = false;
let tutorialBalloon = null;

const TUTORIAL_STEPS = [
  {
    message: "🎯 欢迎来到爱心烟花游戏！点击气球开始游戏",
    duration: 5000,
    action: "click_balloon",
    highlight: true
  },
  {
    message: "🔥 太棒了！连续击中可获得连击加成",
    duration: 4000,
    action: "get_combo",
    highlight: false
  },
  {
    message: "💥 快速双击触发爆炸攻击",
    duration: 4000,
    action: "use_burst",
    highlight: true
  },
  {
    message: "🌟 收集道具获得特殊能力",
    duration: 4000,
    action: "collect_powerup",
    highlight: false
  },
  {
    message: "🎉 完美！尽情享受游戏吧",
    duration: 4000,
    action: "complete",
    highlight: false
  }
];

// 教程高亮系统
class TutorialHighlight {
  constructor(x, y, radius, message) {
    this.x = x;
    this.y = y;
    this.radius = radius;
    this.message = message;
    this.pulsePhase = 0;
    this.alpha = 255;
  }

  update() {
    this.pulsePhase += 0.05;
  }

  show() {
    push();

    // 暗化背景
    fill(0, 0, 0, 150);
    rect(0, 0, width, height);

    // 高亮圆形区域
    blendMode(MULTIPLY);
    fill(255);
    ellipse(this.x, this.y, this.radius * 2);
    blendMode(BLEND);

    // 脉动边框
    let pulseRadius = this.radius + sin(this.pulsePhase) * 20;
    noFill();
    stroke(255, 255, 0, 200);
    strokeWeight(4);
    ellipse(this.x, this.y, pulseRadius * 2);

    // 指示箭头
    this.drawArrow();

    // 提示文字
    fill(255, 255, 255, 240);
    textAlign(CENTER, CENTER);
    textSize(getResponsiveFontSize('large'));

    // 文字背景
    let messageWidth = textWidth(this.message) + 40;
    let textHeight = 60;
    let textX = this.x;
    let textY = this.y - this.radius - 80;

    // 确保文字在屏幕内
    textX = constrain(textX, messageWidth/2, width - messageWidth/2);
    textY = constrain(textY, textHeight/2, height - textHeight/2);

    fill(0, 0, 0, 200);
    rect(textX - messageWidth/2, textY - textHeight/2, messageWidth, textHeight, 15);

    fill(255, 255, 255);
    text(this.message, textX, textY);

    pop();
  }

  drawArrow() {
    push();
    translate(this.x, this.y - this.radius - 40);

    // 动画箭头
    let bounce = sin(this.pulsePhase * 2) * 5;
    translate(0, bounce);

    fill(255, 255, 0);
    noStroke();

    // 箭头形状
    triangle(0, 0, -10, -20, 10, -20);
    rect(-2, -20, 4, 15);

    pop();
  }
}

// 更新教程系统 - 增强版
function updateTutorial() {
  if (!tutorialActive || tutorialStep >= TUTORIAL_STEPS.length) {
    // 清理教程高亮
    if (tutorialHighlight) {
      tutorialHighlight = null;
    }
    return;
  }

  let currentStep = TUTORIAL_STEPS[tutorialStep];

  // 更新教程高亮
  if (tutorialHighlight) {
    tutorialHighlight.update();
  }

  // 检查教程进度条件
  if (checkTutorialProgress(currentStep)) {
    advanceTutorial();
    return;
  }

  // 修复：使用实际的帧间隔时间而不是假设60fps
  let currentFPS = frameRate();
  if (!currentFPS || currentFPS <= 0 || isNaN(currentFPS)) {
    currentFPS = 60; // 提供安全的默认值
  }
  let deltaTime = 1000 / currentFPS;
  tutorialTimer += deltaTime;

  // 超时自动进入下一步
  if (tutorialTimer >= currentStep.duration) {
    advanceTutorial();
  }
}

// 检查教程进度条件
function checkTutorialProgress(step) {
  switch(step.action) {
    case "click_balloon":
      return balloonsPoppedCount > 0;
    case "get_combo":
      return combo >= 2;
    case "use_burst":
      return burstCooldown > 0; // 使用过爆炸攻击
    case "collect_powerup":
      return Object.values(achievements).some(a => a === true);
    case "complete":
      return true;
    default:
      return false;
  }
}

// 推进教程
function advanceTutorial() {
  tutorialStep++;
  tutorialTimer = 0;

  if (tutorialStep < TUTORIAL_STEPS.length) {
    let nextStep = TUTORIAL_STEPS[tutorialStep];
    showToast(nextStep.message, "tutorial");

    // 创建高亮提示
    if (nextStep.highlight) {
      createTutorialHighlight(nextStep);
    } else {
      tutorialHighlight = null;
    }
  } else {
    // 教程完成
    tutorialActive = false;
    tutorialHighlight = null;
    showToast("✨ 教程完成！尽情享受游戏吧！", "complete");

    // 解锁成就
    if (!achievements.firstClick) {
      achievements.firstClick = true;
      showToast("🌟 新手玩家达成！", "achievement");
    }
  }
}

// 创建教程高亮
function createTutorialHighlight(step) {
  switch(step.action) {
    case "click_balloon":
      // 高亮最近的气球
      if (balloons.length > 0) {
        let closestBalloon = balloons[0];
        tutorialHighlight = new TutorialHighlight(
          closestBalloon.pos.x,
          closestBalloon.pos.y,
          closestBalloon.r + 30,
          "点击这个气球！"
        );
      }
      break;
    case "use_burst":
      // 高亮屏幕中央
      tutorialHighlight = new TutorialHighlight(
        width / 2,
        height / 2,
        100,
        "快速双击这里使用爆炸攻击！"
      );
      break;
  }
}

// 绘制教程高亮
function drawTutorialHighlight() {
  if (tutorialHighlight && tutorialActive) {
    tutorialHighlight.show();
  }
}

// 简化的教程高亮（修复性能问题）
function drawSimpleTutorialHighlight() {
  if (!tutorialHighlight) return;

  push();

  // 简单的高亮圆圈
  noFill();
  stroke(255, 255, 0, 200);
  strokeWeight(3);
  ellipse(tutorialHighlight.x, tutorialHighlight.y, tutorialHighlight.radius * 2);

  // 简单的提示文字
  fill(255, 255, 255);
  textAlign(CENTER, CENTER);
  textSize(16);

  let textY = tutorialHighlight.y - tutorialHighlight.radius - 40;
  textY = constrain(textY, 30, height - 30);

  // 文字背景
  let message = tutorialHighlight.message || "点击这里";
  let textW = textWidth(message) + 20;
  fill(0, 0, 0, 200);
  rect(tutorialHighlight.x - textW/2, textY - 15, textW, 30, 10);

  // 文字
  fill(255, 255, 255);
  text(message, tutorialHighlight.x, textY);

  pop();
}

// 视觉反馈增强
function drawTargetingReticle() {
  if (gameState !== "playing") return;
  
  // 在鼠标位置绘制瞄准镜
  push();
  translate(mouseX, mouseY);
  
  stroke(255, 255, 255, 150);
  strokeWeight(2);
  noFill();
  
  // 十字线
  line(-15, 0, -5, 0);
  line(15, 0, 5, 0);
  line(0, -15, 0, -5);
  line(0, 15, 0, 5);
  
  // 外圈
  ellipse(0, 0, 30, 30);
  
  // 爆炸范围提示（双击时）
  if (burstCooldown <= 0) {
    stroke(255, 100, 100, 100);
    strokeWeight(1);
    ellipse(0, 0, burstRadius * 2, burstRadius * 2);
  }
  
  pop();
}

// 动态帮助系统
let lastHintTime = 0;
let burstReadyHintShown = false; // 跟踪爆炸就绪提示是否已显示
function checkAndShowHints() {
  let currentTime = millis();
  
  // 如果玩家长时间没有行动，显示提示
  if (currentTime - lastClickTime > 10000 && balloons.length > 0 && currentTime - lastHintTime > 15000) {
    showToast("💡 提示：点击气球来射击它们！", "hint");
    lastHintTime = currentTime;
  }
  
  // 如果连续失误，给出建议
  if (missCount > 3 && currentTime - lastHintTime > 8000) {
    showToast("🎯 建议：瞄准气球中心更容易命中！", "hint");
    missCount = Math.max(0, missCount - 2); // 减少失误计数避免重复提示
    lastHintTime = currentTime;
  }
  
  // 如果爆炸冷却完成，提醒玩家
  if (burstCooldown <= 0 && !burstReadyHintShown) {
    showToast("💥 爆炸攻击已就绪！双击使用！", "hint");
    burstReadyHintShown = true;
  } else if (burstCooldown > 0) {
    burstReadyHintShown = false; // 重置标志，为下次冷却完成做准备
  }
}

function touchEnded() {
  if (touchControls) {
    let currentTime = millis();

    // 修复：使用touchStarted时记录的触摸数量，避免touchEnded时touches数组被清空的问题
    let currentTouchCount = touchStartCount;

    // 检测双指操作或快速双击
    if (currentTouchCount >= 2 || (currentTime - lastTouchTime < 300 && touchCount > 0)) {
      if (gameState === "playing" && burstCooldown <= 0) {
        activateBurstAbility();
      }
    }

    lastTouchTime = currentTime;
    touchCount = currentTouchCount;
    return false;
  }
}
let audioErrorCount = 0;
let lastAudioErrorTime = 0;
const MAX_AUDIO_ERRORS = 5;
const AUDIO_ERROR_RESET_TIME = 60000; // 60秒后重置错误计数

// 修复：统一的音频错误处理函数，添加时间窗口重置机制
function handleAudioError(error, functionName) {
  console.warn(`Error in ${functionName}:`, error);

  let currentTime = millis();

  // 如果距离上次错误超过重置时间，重置错误计数
  if (currentTime - lastAudioErrorTime > AUDIO_ERROR_RESET_TIME) {
    audioErrorCount = 0;
  }

  audioErrorCount++;
  lastAudioErrorTime = currentTime;

  if (audioErrorCount > MAX_AUDIO_ERRORS) {
    soundEnabled = false;
    console.warn('Audio disabled due to repeated errors');
    showToast("🔇 音频已禁用（错误过多）", "warning");
  }
}

// 改进的音频初始化
function initAudio() {
  if (audioInitialized) return true;

  try {
    // 使用更安全的方式检查webkitAudioContext
    let AudioContextClass = window.AudioContext || window.webkitAudioContext;
    audioContext = new AudioContextClass();
    if (audioContext) {
      // 尝试恢复挂起的音频上下文
      if (audioContext.state === 'suspended') {
        const resumeAudio = async () => {
          try {
            await audioContext.resume();
            console.log('Audio context resumed successfully');
          } catch (e) {
            console.warn('Failed to resume audio context:', e);
          }
        };
        resumeAudio();
      }
      audioInitialized = true;
      return true;
    }
  } catch (e) {
    console.warn('Web Audio API not supported:', e);
    audioContext = null;
  }
  return false;
}

// 安全的音频播放函数
function playClickSound(frequency = 800, duration = 0.1) {
  if (!soundEnabled || !audioContext || audioErrorCount > MAX_AUDIO_ERRORS) return;

  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);

    // 清理资源
    oscillator.onended = () => {
      oscillator.disconnect();
      gainNode.disconnect();
    };
  } catch (e) {
    console.warn('Error playing click sound:', e);
    audioErrorCount++;
    if (audioErrorCount > MAX_AUDIO_ERRORS) {
      soundEnabled = false;
      console.warn('Audio disabled due to repeated errors');
    }
  }
}

function playPopSound(pitch = 1.0) {
  if (!soundEnabled || !audioContext || audioErrorCount > MAX_AUDIO_ERRORS) return;
  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filterNode = audioContext.createBiquadFilter();

    oscillator.connect(filterNode);
    filterNode.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(400 * pitch, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800 * pitch, audioContext.currentTime + 0.1);
    oscillator.type = 'square';

    filterNode.type = 'lowpass';
    filterNode.frequency.setValueAtTime(2000, audioContext.currentTime);

    gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.15);
  } catch (e) {
    handleAudioError(e, 'playPopSound');
  }
}

function playMissSound() {
  if (!soundEnabled || !audioContext || audioErrorCount > MAX_AUDIO_ERRORS) return;
  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.3);
    oscillator.type = 'sawtooth';

    gainNode.gain.setValueAtTime(0.08, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  } catch (e) {
    handleAudioError(e, 'playMissSound');
  }
}

function playBossHitSound() {
  if (!soundEnabled || !audioContext || audioErrorCount > MAX_AUDIO_ERRORS) return;
  try {
    const oscillator1 = audioContext.createOscillator();
    const oscillator2 = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator1.connect(gainNode);
    oscillator2.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator1.frequency.setValueAtTime(150, audioContext.currentTime);
    oscillator2.frequency.setValueAtTime(155, audioContext.currentTime);
    oscillator1.type = 'square';
    oscillator2.type = 'square';

    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator1.start(audioContext.currentTime);
    oscillator2.start(audioContext.currentTime);
    oscillator1.stop(audioContext.currentTime + 0.2);
    oscillator2.stop(audioContext.currentTime + 0.2);
  } catch (e) {
    handleAudioError(e, 'playBossHitSound');
  }
}

function playPowerUpSound() {
  if (!soundEnabled || !audioContext || audioErrorCount > MAX_AUDIO_ERRORS) return;
  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.4);
    oscillator.type = 'triangle';

    gainNode.gain.setValueAtTime(0.12, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.4);
  } catch (e) {
    handleAudioError(e, 'playPowerUpSound');
  }
}

// ============= ENHANCED THEME & VISUAL SYSTEM =============

// 修复：使用RGB数组而不是color对象，避免在p5.js加载前调用color函数
const THEME_COLOR_PALETTES = {
  rainbow: [
    [255, 100, 100], [255, 165, 0], [255, 255, 0],
    [100, 255, 100], [100, 100, 255], [255, 100, 255]
  ],
  golden: [
    [255, 215, 0], [255, 223, 0], [255, 140, 0],
    [218, 165, 32], [255, 193, 37]
  ],
  neon: [
    [255, 20, 147], [0, 255, 255], [255, 255, 0],
    [255, 105, 180], [50, 205, 50]
  ],
  flame: [
    [255, 69, 0], [255, 140, 0], [255, 215, 0],
    [255, 99, 71], [255, 160, 122]
  ]
};

let colorIndex = 0; // 用于循环选择颜色

function getThemeColor(baseColor) {
  switch(currentTheme) {
    case 0: // 粉色主题
      return baseColor;
    case 1: // 彩虹主题
      let rainbowColors = THEME_COLOR_PALETTES.rainbow;
      colorIndex = (colorIndex + 1) % rainbowColors.length;
      let rgb1 = rainbowColors[colorIndex];
      return color(rgb1[0], rgb1[1], rgb1[2]);
    case 2: // 金色主题
      let goldenColors = THEME_COLOR_PALETTES.golden;
      colorIndex = (colorIndex + 1) % goldenColors.length;
      let rgb2 = goldenColors[colorIndex];
      return color(rgb2[0], rgb2[1], rgb2[2]);
    case 3: // 霓虹主题
      let neonColors = THEME_COLOR_PALETTES.neon;
      colorIndex = (colorIndex + 1) % neonColors.length;
      let rgb3 = neonColors[colorIndex];
      return color(rgb3[0], rgb3[1], rgb3[2]);
    case 4: // 火焰主题
      let flameColors = THEME_COLOR_PALETTES.flame;
      colorIndex = (colorIndex + 1) % flameColors.length;
      let rgb4 = flameColors[colorIndex];
      return color(rgb4[0], rgb4[1], rgb4[2]);
    default:
      return baseColor;
  }
}

function getThemePalette() {
  switch(currentTheme) {
    case 0: return { primary: color(255, 105, 180), secondary: color(255, 182, 193), accent: color(255, 20, 147) };
    case 1: return { primary: color(random(255), random(255), random(255)), secondary: color(random(255), random(255), random(255)), accent: color(random(255), random(255), random(255)) };
    case 2: return { primary: color(255, 215, 0), secondary: color(255, 223, 0), accent: color(255, 140, 0) };
    case 3: return { primary: color(0, 255, 255), secondary: color(255, 0, 255), accent: color(255, 255, 0) };
    case 4: return { primary: color(255, 69, 0), secondary: color(255, 140, 0), accent: color(255, 215, 0) };
    default: return { primary: color(255, 105, 180), secondary: color(255, 182, 193), accent: color(255, 20, 147) };
  }
}

// ============= ENHANCED VISUAL FEEDBACK SYSTEM =============

// 增强的游戏感觉系统
let cameraShakeX = 0;
let cameraShakeY = 0;
let hitStopUntil = 0;
let flashOverlay = 0;

// 视觉反馈增强
let visualEffects = {
  screenDistortion: 0,
  chromaticAberration: 0,
  bloomIntensity: 0,
  timeScale: 1.0,
  impactRings: [],
  screenCracks: [],
  particleTrails: []
};

// 冲击波环效果类
class ImpactRing {
  constructor(x, y, maxRadius = 100, color = null) {
    this.x = x;
    this.y = y;
    this.radius = 0;
    this.maxRadius = maxRadius;
    this.thickness = 8;
    this.alpha = 255;
    this.lifetime = 30;
    this.maxLifetime = 30;
    this.color = color || getThemeColor(color(255, 255, 255));
  }

  update() {
    this.lifetime--;
    let progress = 1 - (this.lifetime / this.maxLifetime);

    this.radius = easeOutQuart(progress) * this.maxRadius;
    this.alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);
    this.thickness = map(progress, 0, 1, 8, 2);
  }

  show() {
    push();
    noFill();
    stroke(red(this.color), green(this.color), blue(this.color), this.alpha);
    strokeWeight(this.thickness);
    ellipse(this.x, this.y, this.radius * 2);

    // 内圈光晕
    stroke(red(this.color), green(this.color), blue(this.color), this.alpha * 0.3);
    strokeWeight(this.thickness * 2);
    ellipse(this.x, this.y, this.radius * 1.5);
    pop();
  }

  isFinished() {
    return this.lifetime <= 0;
  }
}

// 屏幕裂纹效果类
class ScreenCrack {
  constructor(x, y, direction, length = 100) {
    this.startX = x;
    this.startY = y;
    this.direction = direction;
    this.length = length;
    this.progress = 0;
    this.lifetime = 120; // 2秒
    this.maxLifetime = 120;
    this.branches = [];

    // 创建分支
    for (let i = 0; i < 2; i++) {
      this.branches.push({
        angle: direction + random(-PI/4, PI/4),
        length: length * random(0.3, 0.7),
        startProgress: random(0.3, 0.8)
      });
    }
  }

  update() {
    this.lifetime--;
    this.progress = min(1, this.progress + 0.05);
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 150);

    push();
    stroke(255, 255, 255, alpha);
    strokeWeight(2);

    // 主裂纹
    let endX = this.startX + cos(this.direction) * this.length * this.progress;
    let endY = this.startY + sin(this.direction) * this.length * this.progress;
    line(this.startX, this.startY, endX, endY);

    // 分支裂纹
    for (let branch of this.branches) {
      if (this.progress > branch.startProgress) {
        let branchProgress = (this.progress - branch.startProgress) / (1 - branch.startProgress);
        let branchStartX = this.startX + cos(this.direction) * this.length * branch.startProgress;
        let branchStartY = this.startY + sin(this.direction) * this.length * branch.startProgress;
        let branchEndX = branchStartX + cos(branch.angle) * branch.length * branchProgress;
        let branchEndY = branchStartY + sin(branch.angle) * branch.length * branchProgress;

        stroke(255, 255, 255, alpha * 0.7);
        strokeWeight(1);
        line(branchStartX, branchStartY, branchEndX, branchEndY);
      }
    }

    pop();
  }

  isFinished() {
    return this.lifetime <= 0;
  }
}

// ============= ENHANCED PARTICLE CLASSES =============

// 普通粒子类 - 增强版
class Particle {
  constructor(x, y, customColor = null) {
    this.pos = createVector(x, y);
    this.vel = p5.Vector.random2D().mult(random(1, 5));
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 255;
    this.maxLifetime = 255;
    this.color = customColor || getThemeColor(color(random(255), random(255), random(255)));
    this.hasGlow = random() < 0.3; // 30%概率有发光效果
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    if (millis() < hitStopUntil) return; // 击中停顿效果

    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;

    // 添加轻微的空气阻力
    this.vel.mult(0.99);
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);

    if (this.hasGlow) {
      // 发光效果
      push();
      blendMode(ADD);
      stroke(red(this.color), green(this.color), blue(this.color), alpha * 0.3);
      strokeWeight(this.r * 2);
      point(this.pos.x, this.pos.y);
      pop();
    }

    stroke(red(this.color), green(this.color), blue(this.color), alpha);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 分数弹出类
class ScorePopup {
  constructor(x, y, value, type = "normal") {
    this.pos = createVector(x, y);
    this.startY = y;
    this.value = value;
    this.lifetime = 60; // 1秒显示时间
    this.maxLifetime = 60;
    this.type = type;
    this.scale = 1;
  }

  update() {
    if (millis() < hitStopUntil) return;

    this.lifetime--;
    let progress = 1 - (this.lifetime / this.maxLifetime);

    // 向上浮动
    this.pos.y = this.startY - easeOutQuart(progress) * 50;

    // 缩放动画
    if (progress < 0.2) {
      this.scale = easeOutQuart(progress / 0.2) * 1.2;
    } else if (progress > 0.8) {
      this.scale = 1.2 * (1 - easeInOutCubic((progress - 0.8) / 0.2));
    } else {
      this.scale = 1.2;
    }
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);

    push();
    translate(this.pos.x, this.pos.y);
    scale(this.scale);

    // 文字阴影
    fill(0, alpha * 0.5);
    textAlign(CENTER, CENTER);
    textSize(16);
    text("+" + this.value, 2, 2);

    // 主文字
    let textColor = color(255, 255, 255);
    if (this.type === "combo") textColor = color(255, 215, 0);
    else if (this.type === "special") textColor = color(255, 100, 255);
    else if (this.type === "boss") textColor = color(255, 50, 50);

    fill(red(textColor), green(textColor), blue(textColor), alpha);
    text("+" + this.value, 0, 0);

    pop();
  }

  isFinished() {
    return this.lifetime <= 0;
  }
}

// 实用优先的Toast系统
class Toast {
  constructor(message, type = "achievement") {
    this.message = message;
    this.type = type;
    this.lifetime = 180; // 3秒
    this.maxLifetime = 180;

    // 智能尺寸设置
    this.fontSize = width <= 600 ? 15 : 17; // 稍微增大字体
    this.width = this.getWidth();
    this.height = this.getHeight(); // 动态高度

    // 简化位置计算（在尺寸计算之后）
    this.calculatePosition();

    // 计算安全位置
    this.currentY = this.calculateSafeY();
    this.targetY = this.currentY;

    // 简化动画属性
    this.alpha = 0;
  }

  // 优化位置计算 - 确保完整显示
  calculatePosition() {
    if (width <= 600) {
      // 移动设备：居中显示
      this.x = width / 2;
      this.targetX = width / 2;
      this.startFromTop = true;
    } else {
      // 桌面：从右侧滑入，但确保有足够空间
      this.x = width + this.width;
      this.targetX = width - this.width / 2 - 30; // 确保不贴边
    }
  }

  // 简化宽度计算 - 固定尺寸避免复杂计算
  getWidth() {
    // 简单固定宽度，避免复杂的文字宽度计算
    if (width <= 600) {
      return Math.min(width * 0.8, 300);
    } else {
      return 350;
    }
  }

  // 简化高度计算 - 固定高度避免复杂计算
  getHeight() {
    // 根据文字长度简单估算高度
    let estimatedLines = Math.ceil(this.message.length / 20); // 简单估算
    estimatedLines = Math.min(estimatedLines, 3); // 最多3行
    return 40 + estimatedLines * 20; // 基础高度 + 行数 * 行高
  }

  // 简化安全Y位置计算 - 避免复杂逻辑
  calculateSafeY() {
    let baseY = width <= 600 ? 80 : 100;
    let spacing = 80; // 固定间距

    // 简单计算：基础位置 + 现有Toast数量 * 间距
    let existingCount = 0;
    try {
      existingCount = toasts.filter(t => t && t.lifetime > 0 && t !== this).length;
    } catch (e) {
      existingCount = 0; // 安全回退
    }

    let targetY = baseY + existingCount * spacing;

    // 确保不超出屏幕
    return Math.min(targetY, height - 100);
  }

  // 简化更新方法
  update() {
    this.lifetime--;
    let progress = 1 - (this.lifetime / this.maxLifetime);

    // 简单动画
    if (progress < 0.2) {
      // 入场
      let t = progress / 0.2;
      this.alpha = t * 255;

      if (this.startFromTop) {
        this.currentY = lerp(-this.height, this.targetY, t);
      } else {
        this.x = lerp(width + this.width, this.targetX, t);
      }
    } else if (progress > 0.8) {
      // 退场
      let t = (progress - 0.8) / 0.2;
      this.alpha = (1 - t) * 255;

      if (this.startFromTop) {
        this.currentY = lerp(this.targetY, -this.height, t);
      } else {
        this.x = lerp(this.targetX, width + this.width, t);
      }
    } else {
      // 稳定显示
      this.alpha = 255;
    }
  }

  // 简化显示方法 - 避免复杂计算
  show() {
    if (this.alpha <= 0) return;

    push();

    // 简单位置计算
    let panelX = this.x - this.width / 2;
    let panelY = this.currentY - this.height / 2;

    // 确保在屏幕内
    panelX = constrain(panelX, 10, width - this.width - 10);
    panelY = constrain(panelY, 10, height - this.height - 10);

    // 简单背景
    fill(0, 0, 0, this.alpha * 0.85);
    stroke(255, 255, 255, this.alpha * 0.4);
    strokeWeight(1);
    rect(panelX, panelY, this.width, this.height, 12);

    // 类型指示条
    let typeColor = this.getTypeColor();
    fill(typeColor[0], typeColor[1], typeColor[2], this.alpha * 0.8);
    noStroke();
    rect(panelX, panelY, 4, this.height, 12, 0, 0, 12);

    // 简单文字显示 - 不换行
    fill(255, 255, 255, this.alpha);
    textAlign(CENTER, CENTER);
    textSize(this.fontSize);

    // 直接显示文字，不进行复杂处理
    text(this.message, panelX + this.width / 2, panelY + this.height / 2);

    pop();
  }

  // 获取类型颜色
  getTypeColor() {
    const colors = {
      achievement: [255, 215, 0],
      boss: [255, 80, 80],
      fever: [255, 140, 0],
      perfect: [200, 100, 255],
      warning: [255, 165, 0],
      info: [100, 200, 255],
      mobile: [80, 255, 120],
      tutorial: [150, 200, 255]
    };
    return colors[this.type] || [255, 255, 255];
  }



  isFinished() {
    return this.lifetime <= 0;
  }
}

// 爱心形状粒子类 - 增强版
class HeartParticle {
  constructor(x, y, customColor = null) {
    this.pos = createVector(x, y);
    this.createHeartVelocity();
    this.acc = createVector(0, 0);
    this.r = random(2, 6);
    this.lifetime = 300;
    this.maxLifetime = 300;
    this.color = customColor || getThemeColor(color(random(200, 255), random(50, 150), random(100, 200)));
    this.rotation = random(TWO_PI);
    this.rotationSpeed = random(-0.1, 0.1);
    this.hasTrail = random() < 0.4; // 40%概率有拖尾
    this.trail = [];
  }

  createHeartVelocity() {
    let t = random(TWO_PI);
    let scale = random(3, 8);
    let x = 16 * pow(sin(t), 3);
    let y = -(13 * cos(t) - 5 * cos(2*t) - 2 * cos(3*t) - cos(4*t));
    this.vel = createVector(x * scale / 30, y * scale / 30);
    this.vel.add(random(-1, 1), random(-1, 1));
    let speed = this.vel.mag();
    if (speed > 0) {
      this.vel.setMag(random(2, 6));
    }
  }

  applyForce(force) {
    this.acc.add(force);
  }

  update() {
    if (millis() < hitStopUntil) return;

    // 记录拖尾
    if (this.hasTrail && frameCount % 2 === 0) {
      this.trail.push({
        x: this.pos.x,
        y: this.pos.y,
        life: 20
      });
      if (this.trail.length > 8) this.trail.shift();
    }

    // 更新拖尾
    for (let t of this.trail) {
      t.life--;
    }
    this.trail = this.trail.filter(t => t.life > 0);

    this.vel.add(this.acc);
    this.pos.add(this.vel);
    this.acc.mult(0);
    this.lifetime -= 2;
    this.rotation += this.rotationSpeed;

    // 空气阻力
    this.vel.mult(0.995);
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);

    // 绘制拖尾
    if (this.hasTrail) {
      for (let i = 0; i < this.trail.length; i++) {
        let t = this.trail[i];
        let trailAlpha = map(t.life, 0, 20, 0, alpha * 0.5);
        stroke(red(this.color), green(this.color), blue(this.color), trailAlpha);
        strokeWeight(this.r * 0.5);
        point(t.x, t.y);
      }
    }

    // 主粒子发光效果
    push();
    blendMode(ADD);
    stroke(red(this.color), green(this.color), blue(this.color), alpha * 0.3);
    strokeWeight(this.r * 1.5);
    point(this.pos.x, this.pos.y);
    pop();

    // 主粒子
    stroke(red(this.color), green(this.color), blue(this.color), alpha);
    strokeWeight(this.r);
    point(this.pos.x, this.pos.y);
  }

  isFinished() {
    return this.lifetime < 0;
  }
}

// 星星类 - 增强版支持视差
class Star {
  constructor(layer = 1) {
    this.pos = createVector(random(width), random(height));
    this.brightness = random(100, 255);
    this.twinkleSpeed = random(0.02, 0.05);
    this.layer = layer; // 1=远景, 2=中景, 3=近景
    this.size = random(0.5, 2) * layer;
    this.speed = 0.1 * layer; // 近景移动更快
    this.color = layer === 1 ? color(200, 200, 255) :
                 layer === 2 ? color(255, 255, 200) :
                 color(255, 200, 255);
  }

  update() {
    this.brightness = 150 + sin(millis() * this.twinkleSpeed) * 100;

    // 视差移动
    this.pos.y += this.speed;
    if (this.pos.y > height + 10) {
      this.pos.y = -10;
      this.pos.x = random(width);
    }
  }

  show() {
    let alpha = this.brightness * (0.3 + 0.7 * this.layer / 3);

    // 发光效果
    if (this.layer === 3) {
      push();
      blendMode(ADD);
      fill(red(this.color), green(this.color), blue(this.color), alpha * 0.3);
      noStroke();
      ellipse(this.pos.x, this.pos.y, this.size * 2);
      pop();
    }

    fill(red(this.color), green(this.color), blue(this.color), alpha);
    noStroke();
    ellipse(this.pos.x, this.pos.y, this.size);
  }
}

// 流星类
class ShootingStar {
  constructor() {
    this.pos = createVector(random(width), -50);
    this.vel = createVector(random(-2, 2), random(3, 8));
    this.lifetime = random(60, 120);
    this.maxLifetime = this.lifetime;
    this.trail = [];
  }

  update() {
    if (millis() < hitStopUntil) return;

    // 记录拖尾
    this.trail.push({
      x: this.pos.x,
      y: this.pos.y,
      life: 15
    });
    if (this.trail.length > 10) this.trail.shift();

    // 更新拖尾
    for (let t of this.trail) {
      t.life--;
    }
    this.trail = this.trail.filter(t => t.life > 0);

    this.pos.add(this.vel);
    this.lifetime--;
  }

  show() {
    let alpha = map(this.lifetime, 0, this.maxLifetime, 0, 255);

    // 绘制拖尾
    for (let i = 0; i < this.trail.length; i++) {
      let t = this.trail[i];
      let trailAlpha = map(t.life, 0, 15, 0, alpha * 0.8);
      let trailSize = map(i, 0, this.trail.length - 1, 1, 4);

      fill(255, 255, 200, trailAlpha);
      noStroke();
      ellipse(t.x, t.y, trailSize);
    }

    // 主体
    push();
    blendMode(ADD);
    fill(255, 255, 200, alpha);
    noStroke();
    ellipse(this.pos.x, this.pos.y, 6);
    pop();

    fill(255, 255, 255, alpha);
    noStroke();
    ellipse(this.pos.x, this.pos.y, 3);
  }

  isFinished() {
    return this.lifetime <= 0 || this.pos.y > height + 50;
  }
}

// 气球类 - 增强版支持多种类型
class Balloon {
  constructor(type = "normal") {
    this.pos = createVector(random(width), height + 50);
    this.vel = createVector(random(-0.5, 0.5), random(-1, -2));
    this.type = type;
    this.bobOffset = random(TWO_PI);
    this.bobSpeed = random(0.02, 0.05);
    this.hitFlash = 0; // 被击中时的闪光效果
    this.scale = 1; // 缩放动画
    this.targetScale = 1;
    this.canSplit = true; // 默认可以分裂，防止无限分裂
    this.splitCount = 0; // 初始化分裂次数

    if (type === "special") {
      this.r = random(40, 60);
      this.color = color(255, 215, 0);
      this.points = 50;
      this.vel.mult(0.7);
    } else if (type === "boss") {
      this.r = 80;
      this.color = color(255, 0, 100);
      this.points = 100;
      this.vel.mult(0.5);
      // 修复冗余的Math.max逻辑：Boss血量 = 基础血量5 + 关卡加成
      this.health = 5 + Math.floor(currentLevel / 2);
      this.maxHealth = this.health;
      maxBossHealth = this.health;
    } else if (type === "shielded") {
      this.r = random(35, 55);
      this.color = color(100, 150, 255);
      this.points = 40;
      this.shield = true;
      this.shieldAlpha = 255;
    } else if (type === "splitter") {
      this.r = random(50, 70);
      this.color = color(255, 100, 255);
      this.points = 60;
      this.vel.mult(0.8);
    } else if (type === "fast") {
      this.r = random(25, 40);
      this.color = color(255, 50, 50);
      this.points = 35;
      this.vel.mult(1.5);
      this.bobSpeed *= 2;
    } else if (type === "sneaky") {
      this.r = random(30, 45);
      this.color = color(150, 150, 150);
      this.points = 45;
      this.alpha = 100; // 半透明
      this.fadeDirection = 1;
    } else {
      this.r = random(30, 50);
      this.color = getThemeColor(color(random(255), random(255), random(255)));
      this.points = 25;
    }
  }

  update() {
    if (millis() < hitStopUntil) return;

    this.pos.add(this.vel);
    this.pos.x += sin(millis() * this.bobSpeed + this.bobOffset) * 0.3;

    // 缩放动画
    this.scale = lerp(this.scale, this.targetScale, 0.1);

    // 击中闪光衰减
    if (this.hitFlash > 0) {
      this.hitFlash -= 5;
    }

    // 特殊类型更新
    if (this.type === "sneaky") {
      this.alpha += this.fadeDirection * 2;
      if (this.alpha >= 200) this.fadeDirection = -1;
      if (this.alpha <= 50) this.fadeDirection = 1;
    }

    if (this.type === "shielded" && this.shield) {
      this.shieldAlpha = 150 + sin(millis() * 0.01) * 105;
    }
  }

  show() {
    push();
    translate(this.pos.x, this.pos.y);
    scale(this.scale);

    // 击中闪光效果
    if (this.hitFlash > 0) {
      push();
      blendMode(ADD);
      fill(255, 255, 255, this.hitFlash);
      noStroke();
      ellipse(0, 0, this.r * 2.5, this.r * 2.7);
      pop();
    }

    // 根据类型绘制不同效果
    if (this.type === "special") {
      // 金色特殊气球闪烁效果
      let alpha = 200 + sin(millis() * 0.01) * 55;

      // 发光光环
      push();
      blendMode(ADD);
      fill(255, 215, 0, alpha * 0.3);
      noStroke();
      ellipse(0, 0, this.r * 2.8, this.r * 3);
      pop();

      fill(red(this.color), green(this.color), blue(this.color), alpha);
      stroke(255, 255, 0);
      strokeWeight(3);
    } else if (this.type === "boss") {
      // Boss气球脉动效果
      let pulse = 1 + sin(millis() * 0.005) * 0.1;
      scale(pulse);

      // Boss光环
      push();
      blendMode(ADD);
      fill(255, 0, 100, 100);
      noStroke();
      ellipse(0, 0, this.r * 3, this.r * 3.2);
      pop();

      fill(this.color);
      stroke(255, 0, 0);
      strokeWeight(4);
    } else if (this.type === "shielded") {
      fill(this.color);
      stroke(100, 150, 255);
      strokeWeight(2);

      // 护盾效果
      if (this.shield) {
        push();
        noFill();
        stroke(100, 200, 255, this.shieldAlpha);
        strokeWeight(3);
        ellipse(0, 0, this.r * 2.6, this.r * 2.8);
        pop();
      }
    } else if (this.type === "splitter") {
      fill(this.color);
      stroke(255, 100, 255);
      strokeWeight(2);

      // 分裂预警效果
      push();
      noFill();
      stroke(255, 100, 255, 100);
      strokeWeight(1);
      for (let i = 0; i < 3; i++) {
        ellipse(0, 0, this.r * (2 + i * 0.3), this.r * (2.2 + i * 0.3));
      }
      pop();
    } else if (this.type === "fast") {
      fill(this.color);
      stroke(255, 100, 100);
      strokeWeight(2);

      // 速度线效果
      push();
      stroke(255, 50, 50, 150);
      strokeWeight(2);
      for (let i = 0; i < 3; i++) {
        line(-this.r - i * 5, 0, -this.r - i * 15, 0);
      }
      pop();
    } else if (this.type === "sneaky") {
      fill(red(this.color), green(this.color), blue(this.color), this.alpha);
      stroke(150, 150, 150, this.alpha);
      strokeWeight(1);
    } else {
      // 普通气球增强视觉
      fill(this.color);
      stroke(255, 255, 255, 200);
      strokeWeight(2);

      // 高光效果
      push();
      fill(255, 255, 255, 100);
      noStroke();
      ellipse(-this.r * 0.3, -this.r * 0.3, this.r * 0.4, this.r * 0.4);
      pop();
    }

    // 主气球体
    ellipse(0, 0, this.r * 2, this.r * 2.2);

    // Boss血量显示
    if (this.type === "boss") {
      fill(255);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(16);
      text(this.health + "/" + this.maxHealth, 0, 0);
    }

    // 气球线（除了Boss）
    if (this.type !== "boss") {
      stroke(100, 100, 100, 150);
      strokeWeight(1);
      line(0, this.r * 1.1, 0, this.r * 1.8);
    }

    pop();
  }

  isOffScreen() {
    return this.pos.y < -this.r;
  }

  isClicked(mx, my) {
    let d = dist(mx, my, this.pos.x, this.pos.y);
    let hitRadius = this.r;

    // 触摸设备增大点击区域
    if (touchControls) {
      hitRadius *= touchFeedback.touchAreaMultiplier;
    }

    return d < hitRadius;
  }

  // 被击中时的效果
  onHit() {
    this.hitFlash = 100;
    this.targetScale = 1.3;
    setTimeout(() => { this.targetScale = 1; }, 100);

    if (this.type === "shielded" && this.shield) {
      this.shield = false;
      return false; // 护盾吸收伤害
    }

    return true; // 造成伤害
  }

  // 分裂气球被击中时创建小气球
  createSplitBalloons() {
    let smallBalloons = [];
    for (let i = 0; i < 3; i++) {
      let smallBalloon = new Balloon("normal"); // 改为normal，防止无限分裂
      smallBalloon.pos = this.pos.copy();
      smallBalloon.r = this.r * 0.4;
      smallBalloon.points = 15;
      smallBalloon.vel = p5.Vector.random2D().mult(random(2, 4));
      smallBalloon.vel.y = abs(smallBalloon.vel.y) * -1; // 向上飞
      smallBalloon.canSplit = false; // 明确标记不能再分裂
      smallBalloons.push(smallBalloon);
    }
    return smallBalloons;
  }
}

// 道具类 - 增强版
class PowerUp {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.vel = createVector(random(-1, 1), random(-1, 1));
    this.r = 25;
    this.lifetime = 450; // 7.5秒生命周期
    this.maxLifetime = 450;
    this.type = random(['double', 'mega', 'slow', 'burst', 'shield']);
    this.bobOffset = random(TWO_PI);
    this.rotation = 0;
    this.scale = 1;
    this.targetScale = 1;
    this.pulsePhase = random(TWO_PI);
  }

  update() {
    if (millis() < hitStopUntil) return;

    this.pos.add(this.vel);
    this.lifetime--;

    // 轻微摆动和旋转
    this.pos.y += sin(millis() * 0.05 + this.bobOffset) * 0.3;
    this.rotation += 0.02;

    // 脉动效果
    this.scale = lerp(this.scale, this.targetScale, 0.1);
    let pulse = 1 + sin(millis() * 0.01 + this.pulsePhase) * 0.1;
    this.scale *= pulse;

    // 即将消失时闪烁
    if (this.lifetime < 90) { // 最后1.5秒
      this.targetScale = (frameCount % 20 < 10) ? 1.2 : 0.8;
    }
  }

  show() {
    push();
    translate(this.pos.x, this.pos.y);
    rotate(this.rotation);
    scale(this.scale);

    let alpha = map(this.lifetime, 0, this.maxLifetime, 50, 255);
    if (this.lifetime < 90) {
      alpha *= (frameCount % 20 < 10) ? 1 : 0.5; // 闪烁效果
    }

    // 发光光环
    push();
    blendMode(ADD);
    let glowColor = this.getGlowColor();
    fill(red(glowColor), green(glowColor), blue(glowColor), alpha * 0.3);
    noStroke();
    ellipse(0, 0, this.r * 3, this.r * 3);
    pop();

    // 根据类型绘制不同的道具
    if (this.type === 'double') {
      fill(255, 215, 0, alpha);
      stroke(255, 255, 0, alpha);
      strokeWeight(3);
      star(0, 0, this.r * 0.7, this.r, 5);

      fill(0, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(12);
      text("2X", 0, 0);
    } else if (this.type === 'mega') {
      fill(255, 100, 255, alpha);
      stroke(255, 150, 255, alpha);
      strokeWeight(3);
      ellipse(0, 0, this.r * 2);

      fill(255, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(10);
      text("MEGA", 0, 0);
    } else if (this.type === 'slow') {
      fill(100, 255, 255, alpha);
      stroke(150, 255, 255, alpha);
      strokeWeight(3);
      rect(-this.r/2, -this.r/2, this.r, this.r, 8);

      fill(0, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(9);
      text("SLOW", 0, 0);
    } else if (this.type === 'burst') {
      fill(255, 50, 50, alpha);
      stroke(255, 100, 100, alpha);
      strokeWeight(3);

      // 爆炸图标
      for (let i = 0; i < 8; i++) {
        let angle = (TWO_PI / 8) * i;
        let x1 = cos(angle) * this.r * 0.3;
        let y1 = sin(angle) * this.r * 0.3;
        let x2 = cos(angle) * this.r * 0.8;
        let y2 = sin(angle) * this.r * 0.8;
        line(x1, y1, x2, y2);
      }

      fill(255, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(9);
      text("BOOM", 0, 0);
    } else if (this.type === 'shield') {
      fill(100, 150, 255, alpha);
      stroke(150, 200, 255, alpha);
      strokeWeight(3);

      // 盾牌形状
      beginShape();
      vertex(0, -this.r);
      vertex(this.r * 0.7, -this.r * 0.3);
      vertex(this.r * 0.7, this.r * 0.5);
      vertex(0, this.r);
      vertex(-this.r * 0.7, this.r * 0.5);
      vertex(-this.r * 0.7, -this.r * 0.3);
      endShape(CLOSE);

      fill(255, alpha);
      noStroke();
      textAlign(CENTER, CENTER);
      textSize(8);
      text("SHIELD", 0, 0);
    }

    pop();
  }

  getGlowColor() {
    switch(this.type) {
      case 'double': return color(255, 215, 0);
      case 'mega': return color(255, 100, 255);
      case 'slow': return color(100, 255, 255);
      case 'burst': return color(255, 50, 50);
      case 'shield': return color(100, 150, 255);
      default: return color(255, 255, 255);
    }
  }

  isFinished() {
    return this.lifetime <= 0;
  }

  isClicked(mx, my) {
    let d = dist(mx, my, this.pos.x, this.pos.y);
    let hitRadius = this.r;

    // 触摸设备增大点击区域
    if (touchControls) {
      hitRadius *= touchFeedback.touchAreaMultiplier;
    }

    return d < hitRadius;
  }
}

// ============= GLOBAL VARIABLES =============

// 性能限制常量
const MAX_PARTICLES = 500; // 最大粒子数量
const MAX_HEART_PARTICLES = 300; // 最大爱心粒子数量
const MAX_BALLOONS = 30; // 最大气球数量，防止无限分裂
const MIN_FPS = 30; // 最低帧率阈值

// 增强的性能监控变量
let frameRateHistory = [];
let lastPerformanceCheck = 0;

// 自适应性能系统
const PERFORMANCE_SETTINGS = {
  HIGH: { maxParticles: 500, maxHeartParticles: 300, effectQuality: 1.0, starCount: [30, 20, 15] },
  MEDIUM: { maxParticles: 300, maxHeartParticles: 200, effectQuality: 0.7, starCount: [20, 15, 10] },
  LOW: { maxParticles: 150, maxHeartParticles: 100, effectQuality: 0.5, starCount: [15, 10, 5] }
};

let currentPerformanceLevel = "HIGH";
let performanceCheckInterval = 0;
let lastFPSCheck = 0;

// 核心游戏对象
let particles = [];
let heartParticles = [];
let scorePopups = [];
let toasts = [];
let gravity = null; // 将在setup()中初始化，使用前会检查并提供默认值
let heartsToDraw = [];
let balloons = [];
let stars = [];
let shootingStars = [];
let powerUps = [];
let specialEvents = []; // 特殊事件数组
let playerPerformanceHistory = []; // 性能历史数组

// 游戏状态
let lastClickTime = 0;
let score = 0;
let combo = 0;
let comboTimer = 0;
let balloonsPoppedCount = 0;
let missCount = 0;
let maxMisses = 3;

// 游戏模式和计时
let gameMode = "freeplay"; // freeplay, timeattack, mission
let timeLeft = 60;
let gameTimer = 0;
let gameStartTime = 0;
let timeAttackStartTime = 0; // 时间攻击模式专用开始时间

// 特效系统
let activeEffect = { type: null, until: 0 };
let feverMode = false;
let feverUntil = 0;
let burstCooldown = 0;
let burstRadius = 100;

// 道具效果管理
let powerUpEffects = {
  slowMotion: { active: false, until: 0, originalGravity: null },
  burstEnhance: { active: false, until: 0, originalRadius: 100 }
};

// 双击检测
let clickCount = 0;
let doubleClickTime = 0;
let doubleClickThreshold = 300; // 300ms内的两次点击视为双击

// 视觉和主题
let currentTheme = 0; // 0-4: 不同主题
let isAutoFireworkPaused = false;
let showUI = true;
let buttons = [];

// 音频
let soundEnabled = true;
let audioContext;
let audioInitialized = false;

// 成就系统
let achievements = {
  firstClick: false,
  combo3: false,
  combo5: false,
  combo10: false,
  score100: false,
  score500: false,
  score1000: false,
  balloonPopper: false,
  feverMaster: false,
  timeAttacker: false,
  perfectLevel: false,
  bossSlayer: false,
  sharpShooter: false
};

// 关卡系统
let currentLevel = 1;
let levelTarget = 5;
let levelBalloonCount = 0;
let gameState = "playing"; // playing, levelComplete, gameComplete, paused
let levelCompleteTimer = 0;
let totalLevels = 15;

// Boss系统
let bossMode = false;
let bossHealth = 0;
let maxBossHealth = 0;

// 任务系统
let currentMission = null;
let missionProgress = 0;
let coins = 0;

// 商店和升级
let upgrades = {
  burstRadius: 1,
  feverDuration: 1,
  specialChance: 1,
  scoreMultiplier: 1,
  comboTime: 1
};

// ============= SETUP FUNCTION =============

function setup() {
  createCanvas(windowWidth, windowHeight);
  gravity = createVector(0, 0.05);
  background(0);

  // 检测设备类型
  detectMobile();

  // 初始化游戏开始时间
  gameStartTime = millis();

  // 初始化所有数组
  particles = [];
  heartParticles = [];
  scorePopups = [];
  toasts = [];
  heartsToDraw = [];
  balloons = [];
  stars = [];
  shootingStars = [];
  powerUps = [];
  buttons = [];
  // specialEvents和playerPerformanceHistory已在全局声明中初始化

  // 初始化多层背景星星（视差效果）
  let settings = PERFORMANCE_SETTINGS[currentPerformanceLevel];
  for (let layer = 1; layer <= 3; layer++) {
    let starCount = settings.starCount[layer - 1];
    for (let i = 0; i < starCount; i++) {
      stars.push(new Star(layer));
    }
  }

  // 初始化音频系统
  initAudio();

  // 监听用户交互来激活音频上下文
  const startAudio = () => {
    if (audioContext && audioContext.state === 'suspended') {
      audioContext.resume();
    }
    document.removeEventListener('click', startAudio);
    document.removeEventListener('touchstart', startAudio);
  };
  document.addEventListener('click', startAudio);
  document.addEventListener('touchstart', startAudio);

  // 加载保存的数据
  loadGameData();

  // 生成第一个任务
  generateNewMission();

  // 简洁欢迎序列
  setTimeout(() => {
    showWelcomeToast("🎉 欢迎来到爱心烟花游戏！");
  }, 1000);

  if (tutorialActive) {
    setTimeout(() => {
      showToast(TUTORIAL_STEPS[0].message, "tutorial");
    }, 2500);
  }
}

// ============= MAIN DRAW FUNCTION =============

function draw() {
  let pushCalled = false;

  try {
    // 渐进式性能降级，而不是完全跳过渲染
    let currentFPS = frameRate() || 60;
    let performanceMode = "normal";
    if (currentFPS < 15) {
      performanceMode = "emergency";
      currentPerformanceLevel = "LOW";
    } else if (currentFPS < 25) {
      performanceMode = "degraded";
      currentPerformanceLevel = "MEDIUM";
    }

    // 性能监控 - 修复：使用已存在的性能管理函数
    performanceCleanup();

    // 应用屏幕震动
    push();
    pushCalled = true;
    translate(cameraShakeX, cameraShakeY);

    // 背景渐变
    drawBackground();

    // 根据性能模式调整渲染
    if (performanceMode !== "emergency") {
      // 绘制多层星星（视差效果）
      for (let star of stars) {
        star.update();
        star.show();
      }

      // 绘制流星
      if (performanceMode === "normal") {
        updateAndDrawShootingStars();
      }
    } else {
      // 紧急模式：显示性能警告但继续基本渲染
      fill(255, 100, 100, 100);
      textAlign(CENTER, CENTER);
      textSize(16);
      text("性能保护模式", width/2, 30);
    }

    // 绘制瞄准镜
    drawTargetingReticle();

    // 游戏状态逻辑
    if (gameState === "playing") {
      updateGameplay();
      updateTutorial(); // 新手引导
      checkAndShowHints(); // 动态帮助
      triggerRandomEvent(); // 特殊事件
    }

    // 更新特殊事件
    for (let i = specialEvents.length - 1; i >= 0; i--) {
      if (specialEvents[i] && typeof specialEvents[i].update === 'function') {
        specialEvents[i].update();
        if (!specialEvents[i].active) {
          specialEvents.splice(i, 1);
        }
      } else {
        // 移除无效元素
        specialEvents.splice(i, 1);
      }
    }

    // 更新和绘制所有游戏对象
    updateAndDrawGameObjects();

    // 更新和绘制触摸反馈（简化版）
    if (touchControls) {
      updateTouchFeedback();
    }

    // 临时禁用复杂视觉效果以修复性能问题
    // updateVisualEffects();
    // drawVisualEffects();

    // 绘制UI
    drawUI();

    // 简化教程高亮
    if (tutorialActive && tutorialHighlight) {
      drawSimpleTutorialHighlight();
    }

    // 绘制特殊状态界面
    if (gameState === "levelComplete") {
      drawLevelCompleteScreen();
    } else if (gameState === "gameComplete") {
      drawGameCompleteScreen();
    } else if (gameState === "paused") {
      drawPauseScreen();
    }

    // 绘制闪光覆盖层
    if (flashOverlay > 0) {
      fill(255, 255, 255, flashOverlay);
      rect(0, 0, width, height);
      flashOverlay -= 10;
    }

  } catch (error) {
    // 如果出现错误，显示错误信息并继续
    background(10, 10, 30);
    fill(255, 255, 0);
    textAlign(CENTER, CENTER);
    textSize(16);
    text("游戏遇到错误，正在恢复...", width/2, height/2);
    console.error("Draw error:", error);
  } finally {
    // 确保变换矩阵栈平衡
    if (pushCalled) {
      pop();
    }
    // 更新屏幕震动
    updateCameraShake();
    // 更新冷却时间
    updateCooldowns();
    // 深度内存清理
    deepMemoryCleanup();
  }
}

// 极简背景绘制 - 性能优先
function drawBackground() {
  // 使用简单的矩形填充，避免复杂的渐变计算
  background(10, 10, 30);

  // 可选：添加简单的渐变效果（性能允许时）
  if (frameRate() > 45) {
    for (let i = 0; i <= height; i += 8) { // 进一步减少绘制频率
      let inter = map(i, 0, height, 0, 1);
      let alpha = inter * 20;
      stroke(20, 30, 60, alpha);
      line(0, i, width, i);
    }
  }
}

// 更新流星
function updateAndDrawShootingStars() {
  // 随机生成流星
  if (random() < 0.002) { // 0.2%概率每帧
    shootingStars.push(new ShootingStar());
  }

  for (let i = shootingStars.length - 1; i >= 0; i--) {
    shootingStars[i].update();
    shootingStars[i].show();

    if (shootingStars[i].isFinished()) {
      shootingStars.splice(i, 1);
    }
  }
}

// 主要游戏逻辑更新
function updateGameplay() {
  // 时间攻击模式计时
  if (gameMode === "timeattack") {
    if (timeAttackStartTime === 0) {
      timeAttackStartTime = millis(); // 初始化时间攻击开始时间
    }
    let elapsedTime = (millis() - timeAttackStartTime) / 1000;
    timeLeft = Math.max(0, 60 - elapsedTime);
    if (timeLeft <= 0) {
      gameState = "gameComplete";
      return;
    }
  }

  // 生成气球逻辑
  spawnBalloons();

  // 生成道具
  spawnPowerUps();

  // 检查狂热模式结束
  if (feverMode && millis() > feverUntil) {
    feverMode = false;
    showToast("🌟 狂热模式结束！", "info");
  }

  // 检查特效结束
  if (activeEffect.type && millis() > activeEffect.until) {
    activeEffect = { type: null, until: 0 };
  }

  // 检查道具效果结束
  if (powerUpEffects.slowMotion.active && millis() > powerUpEffects.slowMotion.until) {
    if (powerUpEffects.slowMotion.originalGravity) {
      gravity = powerUpEffects.slowMotion.originalGravity;
    }
    powerUpEffects.slowMotion.active = false;
    powerUpEffects.slowMotion.originalGravity = null;
  }

  if (powerUpEffects.burstEnhance.active && millis() > powerUpEffects.burstEnhance.until) {
    burstRadius = powerUpEffects.burstEnhance.originalRadius;
    powerUpEffects.burstEnhance.active = false;
  }
}

// ============= SPECIAL EVENTS & GAME BALANCE SYSTEM =============

// 特殊事件系统

class SpecialEvent {
  constructor(type) {
    this.type = type;
    this.duration = 15000; // 15秒
    this.startTime = millis();
    this.active = true;
  }
  
  update() {
    if (millis() - this.startTime > this.duration) {
      this.active = false;
      this.onEnd();
    }
  }
  
  onEnd() {
    switch(this.type) {
      case "balloonRain":
        showToast("🌈 气球雨结束了！", "event");
        break;
      case "goldenTime":
        showToast("✨ 黄金时间结束！", "event");
        break;
      case "speedBoost":
        showToast("⚡ 加速时间结束！", "event");
        break;
      case "feverTime":
        showToast("🔥 狂热时间结束！", "event");
        break;
    }
  }
  
  isActive() {
    return this.active && (millis() - this.startTime) < this.duration;
  }
}

function triggerRandomEvent() {
  if (random() < 0.003 && specialEvents.length === 0 && currentLevel > 2) { // 0.3%概率，3关后才出现
    let eventTypes = ["balloonRain", "goldenTime", "speedBoost", "feverTime"];
    let eventType = random(eventTypes);
    
    let event = new SpecialEvent(eventType);
    specialEvents.push(event);
    
    switch(eventType) {
      case "balloonRain":
        showToast("🌈 气球雨来了！大量气球即将出现！", "event");
        break;
      case "goldenTime":
        showToast("✨ 黄金时间！所有气球都是特殊气球！", "event");
        break;
      case "speedBoost":
        showToast("⚡ 速度提升！点击冷却时间减半！", "event");
        break;
      case "feverTime":
        showToast("🔥 狂热时间！所有分数翻倍！", "event");
        if (!feverMode) {
          feverMode = true;
          feverUntil = millis() + 15000;
        }
        break;
    }
  }
}

// combo等级系统
function getComboLevel(combo) {
  if (combo >= 15) return { name: "传说", color: color(255, 0, 255), multiplier: 3.0 };
  if (combo >= 10) return { name: "史诗", color: color(255, 100, 0), multiplier: 2.5 };
  if (combo >= 7) return { name: "稀有", color: color(100, 255, 255), multiplier: 2.0 };
  if (combo >= 5) return { name: "优秀", color: color(255, 215, 0), multiplier: 1.5 };
  if (combo >= 3) return { name: "良好", color: color(100, 255, 100), multiplier: 1.2 };
  return { name: "普通", color: color(255, 255, 255), multiplier: 1.0 };
}

// 动态难度调整系统
let difficultyMultiplier = 1.0;

function updateDynamicDifficulty() {
  // 记录玩家表现
  let performanceScore = 0;
  if (balloonsPoppedCount > 0) {
    let accuracy = balloonsPoppedCount / (balloonsPoppedCount + missCount);
    let comboFactor = Math.min(combo / 10, 1);
    performanceScore = (accuracy * 0.7 + comboFactor * 0.3) * 100;
  }
  
  playerPerformanceHistory.push(performanceScore);
  if (playerPerformanceHistory.length > 20) {
    playerPerformanceHistory.shift();
  }
  
  // 计算平均表现
  let avgPerformance = playerPerformanceHistory.length > 0 ?
    playerPerformanceHistory.reduce((a, b) => a + b, 0) / playerPerformanceHistory.length : 0;
  
  // 调整难度倍数
  if (avgPerformance > 80) {
    difficultyMultiplier = Math.min(difficultyMultiplier + 0.02, 1.5); // 提高难度
  } else if (avgPerformance < 40) {
    difficultyMultiplier = Math.max(difficultyMultiplier - 0.03, 0.7); // 降低难度
  }
}

// 生成气球 - 增强动态难度
function spawnBalloons() {
  // 更新动态难度
  updateDynamicDifficulty();
  
  // 检查特殊事件
  let balloonRainActive = specialEvents.some(e => e.type === "balloonRain" && e.isActive());
  let goldenTimeActive = specialEvents.some(e => e.type === "goldenTime" && e.isActive());
  
  // 动态调整生成速度，基于玩家表现
  let baseSpawnRate = max(60, 180 - currentLevel * 6);
  let adjustedSpawnRate = Math.floor(baseSpawnRate / difficultyMultiplier);

  // 气球雨事件时加速生成
  if (balloonRainActive) {
    adjustedSpawnRate = Math.floor(adjustedSpawnRate * 0.3); // 生成速度提升至原来3倍
  }

  // 确保生成速度不为0，防止除零错误
  adjustedSpawnRate = Math.max(adjustedSpawnRate, 1);
  
  let maxBalloons = min(10, 3 + Math.floor(currentLevel / 2));
  if (balloonRainActive) {
    maxBalloons = Math.floor(maxBalloons * 1.5); // 气球雨时允许更多气球
  }

  if (frameCount % adjustedSpawnRate === 0 && balloons.length < maxBalloons) {
    let balloonType = "normal";
    let rand = random();

    // Boss关卡（每5关）
    if (currentLevel % 5 === 0 && !bossMode) {
      balloonType = "boss";
      bossMode = true;
    }
    // 黄金时间事件：所有气球都是特殊气球
    else if (goldenTimeActive) {
      balloonType = "special";
    }
    // 根据关卡和雾度调整特殊气球概率
    else {
      let specialChance = (0.05 + currentLevel * 0.008) * difficultyMultiplier;
      if (rand < specialChance * 0.4) balloonType = "splitter";
      else if (rand < specialChance * 0.7) balloonType = "shielded";
      else if (rand < specialChance * 1.0) balloonType = "fast";
      else if (rand < specialChance * 1.3) balloonType = "sneaky";
      else if (rand < specialChance * 2.0) balloonType = "special";
    }

    let newBalloon = new Balloon(balloonType);
    
    // 根据关卡和难度调整气球速度
    let speedMultiplier = (1 + (currentLevel - 1) * 0.08) * Math.sqrt(difficultyMultiplier);
    newBalloon.vel.mult(speedMultiplier);
    
    balloons.push(newBalloon);
  }
}

// 生成道具
function spawnPowerUps() {
  let spawnChance = 0.001 * upgrades.specialChance; // 基础0.1%概率
  if (random() < spawnChance && powerUps.length < 3) {
    let x = random(100, width - 100);
    let y = random(100, height - 100);
    powerUps.push(new PowerUp(x, y));
  }
}

// 自适应性能管理器
function adaptivePerformanceManager() {
  performanceCheckInterval++;
  
  if (performanceCheckInterval % 120 === 0) { // 每2秒检查一次
    let avgFPS = frameRateHistory.reduce((a, b) => a + b, 0) / frameRateHistory.length;
    
    let newLevel = currentPerformanceLevel;
    if (avgFPS < 25) {
      newLevel = "LOW";
    } else if (avgFPS < 35) {
      newLevel = "MEDIUM";
    } else if (avgFPS > 45) {
      newLevel = "HIGH";
    }
    
    if (newLevel !== currentPerformanceLevel) {
      currentPerformanceLevel = newLevel;
      showToast(`⚙️ 性能模式: ${newLevel}`, "performance");
      
      // 应用新的性能设置
      let settings = PERFORMANCE_SETTINGS[newLevel];
      
      // 修复：使用更高效的数组操作，保留最新的粒子
      if (particles.length > settings.maxParticles) {
        particles = particles.slice(-settings.maxParticles); // 保留最新的粒子
      }
      if (heartParticles.length > settings.maxHeartParticles) {
        heartParticles = heartParticles.slice(-settings.maxHeartParticles); // 保留最新的爱心粒子
      }
      
      // 调整星星数量
      adjustStarCount(settings.starCount);
    }
  }
}

// 调整星星数量以适应性能
function adjustStarCount(targetCounts) {
  for (let layer = 1; layer <= 3; layer++) {
    let currentLayerStars = stars.filter(star => star.layer === layer);
    let targetCount = targetCounts[layer - 1];
    
    if (currentLayerStars.length > targetCount) {
      // 移除多余的星星
      let toRemove = currentLayerStars.length - targetCount;
      for (let i = stars.length - 1; i >= 0 && toRemove > 0; i--) {
        if (stars[i].layer === layer) {
          stars.splice(i, 1);
          toRemove--;
        }
      }
    } else if (currentLayerStars.length < targetCount) {
      // 添加星星
      let needed = targetCount - currentLayerStars.length;
      for (let i = 0; i < needed; i++) {
        stars.push(new Star(layer));
      }
    }
  }
}

// 性能监控和自动清理
function performanceCleanup() {
  // 监控帧率
  let currentFPS = frameRate();
  frameRateHistory.push(currentFPS);
  if (frameRateHistory.length > 60) { // 保持最近60帧的记录
    frameRateHistory.shift();
  }

  // 计算平均帧率
  let avgFPS = frameRateHistory.reduce((a, b) => a + b, 0) / frameRateHistory.length;

  // 如果帧率过低，进行紧急清理
  if (avgFPS < MIN_FPS && millis() - lastPerformanceCheck > 1000) {
    console.warn("Performance warning: Low FPS detected, performing emergency cleanup");

    // 大幅减少粒子数量 - 修复：使用更高效的方法
    if (particles.length > 100) {
      particles.length = 100; // 直接截断数组，保留最新的粒子
    }
    if (heartParticles.length > 50) {
      heartParticles.length = 50; // 直接截断数组，保留最新的爱心粒子
    }

    // 清理过多的气球 - 使用高效的slice操作
    if (balloons.length > MAX_BALLOONS) {
      balloons = balloons.slice(-MAX_BALLOONS);
      console.warn("Emergency balloon cleanup:", balloons.length);
    }

    // 清理其他对象 - 使用高效的slice操作
    if (scorePopups.length > 10) {
      scorePopups = scorePopups.slice(-10);
    }
    if (toasts.length > 5) {
      toasts = toasts.slice(-5);
    }
    if (powerUps.length > 3) {
      powerUps = powerUps.slice(-3);
    }

    lastPerformanceCheck = millis();
    showToast("⚠️ 性能优化已启动", "warning");
  }

  // 常规清理 - 修复：使用更高效的方法
  if (particles.length > MAX_PARTICLES) {
    particles.length = MAX_PARTICLES; // 直接截断，保留最新的粒子
  }

  if (heartParticles.length > MAX_HEART_PARTICLES) {
    heartParticles.length = MAX_HEART_PARTICLES; // 直接截断，保留最新的爱心粒子
  }

  // 超级安全检查 - 防止任何时候卡死
  let totalParticles = particles.length + heartParticles.length;
  if (totalParticles > MAX_PARTICLES + MAX_HEART_PARTICLES) {
    console.warn("CRITICAL: Too many particles detected!", totalParticles);

    // 修复：使用更高效的批量清理方式
    let safeParticleLimit = Math.floor((MAX_PARTICLES + MAX_HEART_PARTICLES) * 0.7);
    if (particles.length > safeParticleLimit / 2) {
      particles = particles.slice(-(safeParticleLimit / 2));
    }
    if (heartParticles.length > safeParticleLimit / 2) {
      heartParticles = heartParticles.slice(-(safeParticleLimit / 2));
    }
  }

  // 清理过期的Toast - 使用高效的slice操作
  if (toasts.length > 10) {
    toasts = toasts.slice(-10);
  }
}

// 更新和绘制所有游戏对象
function updateAndDrawGameObjects() {
  // 性能监控
  performanceCleanup();
  // 更新和绘制道具
  for (let i = powerUps.length - 1; i >= 0; i--) {
    powerUps[i].update();
    powerUps[i].show();
    if (powerUps[i].isFinished()) {
      powerUps.splice(i, 1);
    }
  }

  // 更新和绘制气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    balloons[i].update();
    balloons[i].show();
    if (balloons[i].isOffScreen()) {
      balloons.splice(i, 1);
    }
  }

  // 更新和绘制粒子
  if (!gravity) gravity = createVector(0, 0.05); // 安全检查（移到循环外）
  for (let i = particles.length - 1; i >= 0; i--) {
    particles[i].applyForce(gravity);
    particles[i].update();
    particles[i].show();
    if (particles[i].isFinished()) {
      particles.splice(i, 1);
    }
  }

  // 更新和绘制爱心粒子
  for (let i = heartParticles.length - 1; i >= 0; i--) {
    heartParticles[i].applyForce(gravity);
    heartParticles[i].update();
    heartParticles[i].show();
    if (heartParticles[i].isFinished()) {
      heartParticles.splice(i, 1);
    }
  }

  // 更新和绘制分数弹出
  for (let i = scorePopups.length - 1; i >= 0; i--) {
    scorePopups[i].update();
    scorePopups[i].show();
    if (scorePopups[i].isFinished()) {
      scorePopups.splice(i, 1);
    }
  }

  // 更新和绘制提示
  for (let i = toasts.length - 1; i >= 0; i--) {
    toasts[i].update();
    toasts[i].show();
    if (toasts[i].isFinished()) {
      toasts.splice(i, 1);
    }
  }

  // 绘制临时爱心
  for (let i = heartsToDraw.length - 1; i >= 0; i--) {
    let heart = heartsToDraw[i];
    let currentSize = heart.size * (1 + (20 - heart.lifetime) / 20);
    drawHeart(heart.x, heart.y, currentSize);

    heart.lifetime--;
    if (heart.lifetime <= 0) {
      heartsToDraw.splice(i, 1);
    }
  }

}

// 更新屏幕震动
function updateCameraShake() {
  cameraShakeX *= 0.9;
  cameraShakeY *= 0.9;
  if (abs(cameraShakeX) < 0.1) cameraShakeX = 0;
  if (abs(cameraShakeY) < 0.1) cameraShakeY = 0;
}

// 更新冷却时间
function updateCooldowns() {
  if (burstCooldown > 0) {
    burstCooldown--;
  }
}

// 增强的屏幕震动系统
function addScreenShake(intensity = 5, duration = 300) {
  cameraShakeX += random(-intensity, intensity);
  cameraShakeY += random(-intensity, intensity);

  // 添加屏幕扭曲效果
  visualEffects.screenDistortion = Math.max(visualEffects.screenDistortion, intensity * 0.5);

  // 添加色差效果
  visualEffects.chromaticAberration = Math.max(visualEffects.chromaticAberration, intensity * 0.3);

  // 时间缩放效果（重击时慢动作）
  if (intensity > 10) {
    visualEffects.timeScale = 0.3;
    setTimeout(() => {
      visualEffects.timeScale = 1.0;
    }, 200);
  }
}

// 添加冲击波效果
function addImpactRing(x, y, maxRadius = 100, color = null) {
  let ring = new ImpactRing(x, y, maxRadius, color);
  visualEffects.impactRings.push(ring);

  // 限制冲击波数量
  if (visualEffects.impactRings.length > 5) {
    visualEffects.impactRings.shift();
  }
}

// 添加屏幕裂纹效果
function addScreenCrack(x, y, intensity = 1) {
  for (let i = 0; i < intensity; i++) {
    let direction = random(TWO_PI);
    let length = random(50, 150);
    let crack = new ScreenCrack(x, y, direction, length);
    visualEffects.screenCracks.push(crack);
  }

  // 限制裂纹数量
  if (visualEffects.screenCracks.length > 10) {
    visualEffects.screenCracks = visualEffects.screenCracks.slice(-10);
  }
}

// 添加击中停顿
function addHitStop(duration = 80) {
  hitStopUntil = millis() + duration;
}

// 增强的闪光效果
function addFlash(intensity = 100, color = null) {
  flashOverlay = intensity;

  // 添加光晕效果
  visualEffects.bloomIntensity = Math.max(visualEffects.bloomIntensity, intensity * 0.8);
}

// 更新视觉效果
function updateVisualEffects() {
  // 更新冲击波
  for (let i = visualEffects.impactRings.length - 1; i >= 0; i--) {
    visualEffects.impactRings[i].update();
    if (visualEffects.impactRings[i].isFinished()) {
      visualEffects.impactRings.splice(i, 1);
    }
  }

  // 更新屏幕裂纹
  for (let i = visualEffects.screenCracks.length - 1; i >= 0; i--) {
    visualEffects.screenCracks[i].update();
    if (visualEffects.screenCracks[i].isFinished()) {
      visualEffects.screenCracks.splice(i, 1);
    }
  }

  // 衰减视觉效果
  visualEffects.screenDistortion *= 0.9;
  visualEffects.chromaticAberration *= 0.95;
  visualEffects.bloomIntensity *= 0.92;
}

// 绘制视觉效果
function drawVisualEffects() {
  // 绘制冲击波
  for (let ring of visualEffects.impactRings) {
    ring.show();
  }

  // 绘制屏幕裂纹
  for (let crack of visualEffects.screenCracks) {
    crack.show();
  }

  // 屏幕扭曲效果（简化版）
  if (visualEffects.screenDistortion > 1) {
    push();
    tint(255, 255 - visualEffects.screenDistortion * 2);
    // 这里可以添加更复杂的扭曲效果
    pop();
  }
}

// ============= MOUSE INTERACTION =============

// 处理鼠标点击 - 改进版（支持自定义坐标）
function handleMouseClick(clickX = null, clickY = null) {
  // 修复：根据性能等级动态调整点击限制时间
  let clickThreshold = currentPerformanceLevel === "HIGH" ? 30 :
                      currentPerformanceLevel === "MEDIUM" ? 50 : 80;
  if (millis() - lastClickTime < clickThreshold) return;
  lastClickTime = millis();

  // 使用传入的坐标或默认的鼠标坐标
  let targetX = clickX !== null ? clickX : mouseX;
  let targetY = clickY !== null ? clickY : mouseY;

  let currentTime = millis();

  // 修复：简化双击检测逻辑，避免竞态条件
  let timeSinceLastClick = currentTime - doubleClickTime;
  if (timeSinceLastClick < doubleClickThreshold && clickCount === 1) {
    // 确认双击
    if (gameState === "playing" && burstCooldown <= 0) {
      activateBurstAbility();
    }
    clickCount = 0;
    doubleClickTime = 0;
    return;
  } else {
    // 重置或开始新的点击序列
    clickCount = 1;
    doubleClickTime = currentTime;
  }

  // 普通点击处理
  let hitSomething = false;

  // 检查气球点击
  for (let i = balloons.length - 1; i >= 0; i--) {
    if (balloons[i].isClicked(targetX, targetY)) {
      hitSomething = true;
      handleBalloonHit(balloons[i], i);
      break;
    }
  }

  // 检查道具点击
  if (!hitSomething) {
    for (let i = powerUps.length - 1; i >= 0; i--) {
      if (powerUps[i].isClicked(targetX, targetY)) {
        hitSomething = true;
        handlePowerUpClick(powerUps[i], i);
        break;
      }
    }
  }

  // 如果没有击中任何物体
  if (!hitSomething) {
    handleMiss();
  } else {
    // 击中了某个物体，处理连击
    handleCombo();
  }
}

// 处理气球被击中 - 增强触摸反馈
function handleBalloonHit(balloon, index) {
  let damaged = balloon.onHit();

  // 添加触摸反馈
  if (touchControls) {
    // 根据气球类型选择不同的震动反馈
    if (balloon.type === "boss") {
      triggerTouchVibration('heavy');
      addTouchRipple(balloon.pos.x, balloon.pos.y, color(255, 50, 50));
    } else if (balloon.type === "special") {
      triggerTouchVibration('success');
      addTouchRipple(balloon.pos.x, balloon.pos.y, color(255, 215, 0));
    } else {
      triggerTouchVibration('medium');
      addTouchRipple(balloon.pos.x, balloon.pos.y, color(100, 255, 100));
    }
  }

  if (!damaged && balloon.type === "shielded") {
    // 护盾被击破
    playPopSound(0.8);
    addScreenShake(3);

    // 护盾破碎效果
    for (let j = 0; j < 15; j++) { // 从20减少到15
      let p = new Particle(balloon.pos.x, balloon.pos.y, color(100, 150, 255));
      safeAddParticle(p);
    }

    scorePopups.push(new ScorePopup(balloon.pos.x, balloon.pos.y, 10, "normal"));
    score += 10;
    return;
  }

  // 处理不同类型的气球
  if (balloon.type === "boss") {
    balloon.health--;
    bossHealth = balloon.health;

    playBossHitSound();
    addScreenShake(8);
    addHitStop(120);
    addFlash(50);

    // Boss击中视觉效果
    addImpactRing(balloon.pos.x, balloon.pos.y, 120, color(255, 50, 50));
    if (balloon.health <= 2) {
      addScreenCrack(balloon.pos.x, balloon.pos.y, 2);
    }

    // Boss受伤效果
    for (let j = 0; j < 15; j++) { // 从25减少到15
      let p = new Particle(balloon.pos.x, balloon.pos.y, color(255, 100, 100));
      safeAddParticle(p);
    }

    scorePopups.push(new ScorePopup(balloon.pos.x, balloon.pos.y, 20, "boss"));
    score += 20;

    if (balloon.health <= 0) {
      // Boss被击败
      handleBossDefeat(balloon, index);
    }
  } else {
    // 普通气球被击中
    handleNormalBalloonPop(balloon, index);
  }
}

// 处理Boss被击败
function handleBossDefeat(balloon, index) {
  playPopSound(1.5);
  addScreenShake(15);
  addHitStop(200);
  addFlash(150);

  // Boss击败的震撼视觉效果
  addImpactRing(balloon.pos.x, balloon.pos.y, 200, color(255, 0, 100));
  addImpactRing(balloon.pos.x, balloon.pos.y, 150, color(255, 100, 0));
  addScreenCrack(balloon.pos.x, balloon.pos.y, 5);

  let bonusScore = balloon.points + combo * 20;
  score += bonusScore;
  balloonsPoppedCount++;
  levelBalloonCount++;
  bossMode = false;

  // Boss爆炸效果（减少粒子数量，防止死机）
  for (let j = 0; j < 50; j++) { // 从150减少到50
    let p = new HeartParticle(balloon.pos.x, balloon.pos.y, color(255, 215, 0));
    safeAddHeartParticle(p);
  }

  for (let j = 0; j < 30; j++) { // 从100减少到30
    let p = new Particle(balloon.pos.x, balloon.pos.y, color(255, 0, 100));
    p.vel.mult(3);
    safeAddParticle(p);
  }

  scorePopups.push(new ScorePopup(balloon.pos.x, balloon.pos.y, bonusScore, "boss"));
  balloons.splice(index, 1);

  // 触发Boss杀手成就（修复：在正确的位置检查成就）
  if (!achievements.bossSlayer) {
    achievements.bossSlayer = true;
    showToast("👹 Boss杀手！击败强大的Boss！", "achievement");
    coins += 100;
  }

  showToast("👹 Boss被击败！获得巨额奖励！", "boss");
  checkLevelComplete();
}

// 处理普通气球爆炸
function handleNormalBalloonPop(balloon, index) {
  playPopSound(balloon.type === "special" ? 1.3 : 1.0);
  addScreenShake(balloon.type === "special" ? 8 : 5);
  addHitStop(balloon.type === "special" ? 100 : 60);

  // 普通气球击中视觉效果
  if (balloon.type === "special") {
    addImpactRing(balloon.pos.x, balloon.pos.y, 80, color(255, 215, 0));
  } else {
    addImpactRing(balloon.pos.x, balloon.pos.y, 60, balloon.color);
  }

  let bonusScore = balloon.points + combo * 5;
  if (feverMode) bonusScore *= 2;
  if (activeEffect.type === 'double' && millis() < activeEffect.until) bonusScore *= 2;

  score += bonusScore;
  balloonsPoppedCount++;
  levelBalloonCount++;

  // 创建爆炸效果（减少粒子数量）
  let particleCount = balloon.type === "special" ? 25 : 15; // 减少粒子数量
  for (let j = 0; j < particleCount; j++) {
    let p = new Particle(balloon.pos.x, balloon.pos.y, balloon.color);
    if (balloon.type === "special") p.vel.mult(1.8);
    safeAddParticle(p);
  }

  // 爱心粒子效果
  let heartCount = balloon.type === "special" ? 15 : 8; // 减少爱心粒子数量
  for (let j = 0; j < heartCount; j++) {
    let p = new HeartParticle(balloon.pos.x, balloon.pos.y);
    safeAddHeartParticle(p);
  }

  let popupType = balloon.type === "special" ? "special" : "normal";
  scorePopups.push(new ScorePopup(balloon.pos.x, balloon.pos.y, bonusScore, popupType));

  // 处理分裂气球（加强安全检查，防止无限分裂）
  if (balloon.type === "splitter" &&
      balloon.canSplit !== false &&
      (balloon.splitCount || 0) < 1 &&
      balloons.length < MAX_BALLOONS - 5) { // 增加安全边距
    balloon.canSplit = false; // 立即防止重复分裂
    balloon.splitCount++; // 记录分裂次数

    let splitBalloons = balloon.createSplitBalloons();
    // 双重检查：限制气球总数，防止无限分裂
    if (balloons.length + splitBalloons.length <= MAX_BALLOONS) {
      balloons.push(...splitBalloons);
      showToast("💥 气球分裂了！", "info");
    } else {
      showToast("⚠️ 气球太多，分裂被阻止", "warning");
      console.warn("Balloon split prevented: too many balloons", balloons.length);
    }
  }

  balloons.splice(index, 1);
  checkLevelComplete();
}

// ============= UI DRAWING FUNCTIONS =============

// UI布局配置 - 修复：使用缓存系统提高性能
let UI_LAYOUT_CACHE = null;
let lastScreenSize = { width: 0, height: 0 };

function getUILayout() {
  // 检查屏幕尺寸是否改变，如果改变则重新计算
  if (!UI_LAYOUT_CACHE || lastScreenSize.width !== width || lastScreenSize.height !== height) {
    UI_LAYOUT_CACHE = {
      leftPanel: { x: 20, y: 20, w: 250, h: 180 },
      rightPanel: { x: 20, y: 260, w: 250, h: 120 },
      missionPanel: { x: Math.min(width/2 - 150, width - 570), y: 20, w: 280, h: 70 },
      effectPanel: { x: width - 270, y: 200, w: 250, h: 60 },
      progressBar: { x: 20, y: 220, w: 250, h: 25 },
      burstCooldown: { x: 300, y: height - 60, w: 200, h: 20 },
      bossBar: { x: width/2 - 150, y: 100, w: 300, h: 30 },
      feverBar: { x: width/2 - 100, y: height - 80, w: 200, h: 20 },
      toastBase: { y: 150 }
    };
    lastScreenSize = { width: width, height: height };
  }
  return UI_LAYOUT_CACHE;
}

// 主UI绘制 - 简化版（修复性能问题）
function drawUI() {
  if (!showUI) return;

  // 简化的响应式判断
  if (width < 600) {
    drawSimpleMobileUI();
  } else if (width < 900) {
    drawSimpleTabletUI();
  } else {
    drawHUD();
    drawProgressBars();
    drawComboDisplay();
    drawActiveEffects();
    drawMissionDisplay();
  }
}

// 移动端UI布局
function drawMobileUI() {
  let palette = getThemePalette();
  let spacing = getResponsiveSpacing('small');
  let fontSize = getResponsiveFontSize('small');

  // 顶部信息条 - 全宽度
  let topBarHeight = Math.max(40, 50 * responsiveUI.scale);
  drawPanel(spacing, spacing, width - spacing * 2, topBarHeight, palette.primary);

  fill(255);
  textAlign(LEFT, CENTER);
  textSize(fontSize);
  let centerY = spacing + topBarHeight / 2;

  // 左侧：分数和连击
  text("💗 " + score.toLocaleString(), spacing * 2, centerY - 8);
  text("🎯 " + combo + "x", spacing * 2, centerY + 8);

  // 右侧：关卡信息
  textAlign(RIGHT, CENTER);
  text("关卡 " + currentLevel, width - spacing * 2, centerY - 8);
  text(levelBalloonCount + "/" + levelTarget, width - spacing * 2, centerY + 8);

  // 底部进度条
  let progressY = spacing * 2 + topBarHeight;
  let progressWidth = width - spacing * 4;
  let progress = levelBalloonCount / levelTarget;
  drawProgressBar(spacing * 2, progressY, progressWidth, 20, progress, palette.accent, "");

  // Boss血量条（如果有）
  if (bossMode && bossHealth > 0) {
    let bossProgress = bossHealth / maxBossHealth;
    let bossY = progressY + 30;
    drawProgressBar(spacing * 2, bossY, progressWidth, 25, bossProgress, color(255, 50, 50), "Boss");
  }
}

// 平板UI布局
function drawTabletUI() {
  let palette = getThemePalette();
  let spacing = getResponsiveSpacing('medium');

  // 左侧面板 - 缩小尺寸
  let leftPanelW = 280;
  let leftPanelH = 160;
  drawPanel(spacing, spacing, leftPanelW, leftPanelH, palette.primary);

  fill(255);
  textAlign(LEFT, TOP);
  textSize(getResponsiveFontSize('medium'));
  text("💗 分数: " + score.toLocaleString(), spacing + 15, spacing + 25);
  text("🎯 连击: " + combo + "x", spacing + 15, spacing + 50);
  text("🎈 击破: " + balloonsPoppedCount, spacing + 15, spacing + 75);
  text("🎯 关卡: " + currentLevel + "/" + totalLevels, spacing + 15, spacing + 100);
  text("📊 目标: " + levelBalloonCount + "/" + levelTarget, spacing + 15, spacing + 125);

  // 其他UI元素
  drawProgressBars();
  drawComboDisplay();
  drawActiveEffects();
  drawMissionDisplay();
}

// ========== 星河梦境移动端UI ==========
function drawSimpleMobileUI() {
  push();

  // 现代化顶部信息面板
  let panelHeight = 80;
  let margin = 15;

  // 毛玻璃背景
  fill(0, 0, 0, 120);
  stroke(255, 255, 255, 60);
  strokeWeight(1);
  rect(margin, margin, width - margin * 2, panelHeight, 20);

  // 渐变装饰条
  for (let i = 0; i < 3; i++) {
    fill(102 + i * 50, 126 + i * 30, 234, 100 - i * 30);
    noStroke();
    rect(margin + 5, margin + 5 + i * 2, width - margin * 2 - 10, 2, 1);
  }

  // 左侧信息
  fill(255, 255, 255, 240);
  textAlign(LEFT, CENTER);
  textSize(16);
  textStyle(BOLD);
  text("✨ " + score.toLocaleString(), margin + 20, margin + 25);

  textSize(14);
  textStyle(NORMAL);
  fill(255, 255, 255, 200);
  text("🎯 连击 " + combo + "x", margin + 20, margin + 45);

  // 右侧信息
  textAlign(RIGHT, CENTER);
  textSize(16);
  textStyle(BOLD);
  fill(255, 255, 255, 240);
  text("第 " + currentLevel + " 关", width - margin - 20, margin + 25);

  textSize(14);
  textStyle(NORMAL);
  fill(255, 255, 255, 200);
  text(levelBalloonCount + " / " + levelTarget, width - margin - 20, margin + 45);

  // 进度条
  let progressY = margin + 65;
  let progressWidth = width - margin * 2 - 40;
  let progress = levelBalloonCount / levelTarget;

  // 进度条背景
  fill(0, 0, 0, 100);
  noStroke();
  rect(margin + 20, progressY, progressWidth, 8, 4);

  // 进度条填充
  let gradient = lerpColor(color(102, 126, 234), color(255, 107, 107), progress);
  fill(gradient);
  rect(margin + 20, progressY, progressWidth * progress, 8, 4);

  pop();
}

// ========== 星河梦境平板UI ==========
function drawSimpleTabletUI() {
  push();

  // 现代化左侧信息面板
  let panelWidth = 300;
  let panelHeight = 160;
  let margin = 20;

  // 毛玻璃背景
  fill(0, 0, 0, 100);
  stroke(255, 255, 255, 80);
  strokeWeight(1.5);
  rect(margin, margin, panelWidth, panelHeight, 24);

  // 内部发光效果
  for (let i = 0; i < 3; i++) {
    stroke(102, 126, 234, 40 - i * 10);
    strokeWeight(3 - i);
    noFill();
    rect(margin + i + 1, margin + i + 1,
         panelWidth - (i + 1) * 2, panelHeight - (i + 1) * 2, 24 - i * 2);
  }

  // 标题
  fill(255, 255, 255, 240);
  textAlign(LEFT, TOP);
  textSize(18);
  textStyle(BOLD);
  text("✨ 星河状态", margin + 25, margin + 20);

  // 信息列表
  let infoY = margin + 50;
  let lineHeight = 22;

  textSize(14);
  textStyle(NORMAL);

  // 分数
  fill(255, 215, 0, 220);
  text("💫 能量值", margin + 25, infoY);
  fill(255, 255, 255, 200);
  textAlign(RIGHT, TOP);
  text(score.toLocaleString(), margin + panelWidth - 25, infoY);

  // 连击
  infoY += lineHeight;
  textAlign(LEFT, TOP);
  fill(255, 107, 107, 220);
  text("🔥 连击", margin + 25, infoY);
  fill(255, 255, 255, 200);
  textAlign(RIGHT, TOP);
  text(combo + "x", margin + panelWidth - 25, infoY);

  // 击破数
  infoY += lineHeight;
  textAlign(LEFT, TOP);
  fill(100, 200, 255, 220);
  text("🎈 击破", margin + 25, infoY);
  fill(255, 255, 255, 200);
  textAlign(RIGHT, TOP);
  text(balloonsPoppedCount, margin + panelWidth - 25, infoY);

  // 关卡进度
  infoY += lineHeight;
  textAlign(LEFT, TOP);
  fill(150, 255, 150, 220);
  text("🌟 关卡", margin + 25, infoY);
  fill(255, 255, 255, 200);
  textAlign(RIGHT, TOP);
  text(currentLevel + " / " + totalLevels, margin + panelWidth - 25, infoY);

  pop();
}

// 桌面UI布局
function drawDesktopUI() {
  drawHUD();
  drawProgressBars();
  drawComboDisplay();
  drawActiveEffects();
  drawMissionDisplay();
}

// 绘制HUD面板
function drawHUD() {
  let palette = getThemePalette();
  let layout = getUILayout(); // 修复：使用缓存的布局

  // 左侧面板 - 分数和统计
  let leftPanel = layout.leftPanel;
  drawPanel(leftPanel.x, leftPanel.y, leftPanel.w, leftPanel.h, palette.primary);

  fill(255);
  textAlign(LEFT, TOP);
  textSize(16); // 恢复原来的字体大小
  text("💗 分数: " + score.toLocaleString(), leftPanel.x + 15, leftPanel.y + 25);
  text("🎯 连击: " + combo + "x", leftPanel.x + 15, leftPanel.y + 50);
  text("✨ 粒子: " + (particles.length + heartParticles.length), leftPanel.x + 15, leftPanel.y + 75);
  text("🎈 气球: " + balloonsPoppedCount, leftPanel.x + 15, leftPanel.y + 100);
  text("💰 金币: " + coins, leftPanel.x + 15, leftPanel.y + 125);

  // 左侧 - 关卡信息面板（在进度条下方）
  let levelInfoPanel = layout.rightPanel; // 重用rightPanel配置，但现在在左侧
  let levelInfoX = levelInfoPanel.x;
  drawPanel(levelInfoX, levelInfoPanel.y, levelInfoPanel.w, levelInfoPanel.h, palette.secondary);

  fill(255);
  textAlign(LEFT, TOP);
  textSize(18);
  text("🏆 关卡: " + currentLevel + "/" + totalLevels, levelInfoX + 15, levelInfoPanel.y + 25);

  if (gameMode === "timeattack") {
    text("⏰ 时间: " + Math.ceil(timeLeft) + "s", levelInfoX + 15, levelInfoPanel.y + 55);
    text("🎯 目标: " + levelBalloonCount + "/" + levelTarget, levelInfoX + 15, levelInfoPanel.y + 85);
  } else {
    text("🎯 目标: " + levelBalloonCount + "/" + levelTarget, levelInfoX + 15, levelInfoPanel.y + 55);
  }

  // 显示失误计数
  if (missCount > 0) {
    fill(255, 100, 100);
    text("❌ 失误: " + missCount, levelInfoX + 15, gameMode === "timeattack" ? levelInfoPanel.y + 115 : levelInfoPanel.y + 85);
  }
}

// 绘制面板
function drawPanel(x, y, w, h, accentColor) {
  // 背景
  fill(0, 0, 0, 150);
  stroke(255, 255, 255, 50);
  strokeWeight(1);
  rect(x, y, w, h, 15);

  // 顶部装饰条
  fill(red(accentColor), green(accentColor), blue(accentColor), 100);
  noStroke();
  rect(x, y, w, 8, 15, 15, 0, 0);
}

// 绘制进度条
function drawProgressBars() {
  let palette = getThemePalette();
  let layout = getUILayout(); // 修复：使用缓存的布局

  // 关卡进度条
  let progress = levelBalloonCount / levelTarget;
  let progressBar = layout.progressBar;
  drawProgressBar(progressBar.x, progressBar.y, progressBar.w, progressBar.h, progress, palette.accent, "关卡进度");

  // Boss血量条
  if (bossMode && bossHealth > 0) {
    let bossProgress = bossHealth / maxBossHealth;
    let bossBar = layout.bossBar;
    let bossX = bossBar.x;
    drawProgressBar(bossX, bossBar.y, bossBar.w, bossBar.h, bossProgress, color(255, 50, 50), "Boss血量");
  }

  // 狂热模式进度条
  if (feverMode) {
    let feverProgress = (feverUntil - millis()) / (7000 * upgrades.feverDuration);
    let feverBar = layout.feverBar;
    let feverX = feverBar.x;
    let feverY = feverBar.y;
    drawProgressBar(feverX, feverY, feverBar.w, feverBar.h, feverProgress, color(255, 215, 0), "狂热模式");
  }

  // 爆炸冷却条
  if (burstCooldown > 0) {
    let cooldownProgress = 1 - (burstCooldown / 300); // 5秒冷却
    let cooldownBar = layout.burstCooldown;
    let cooldownY = cooldownBar.y;
    drawProgressBar(cooldownBar.x, cooldownY, cooldownBar.w, cooldownBar.h, cooldownProgress, color(255, 100, 100), "爆炸冷却");
  }
}

// 绘制单个进度条
function drawProgressBar(x, y, w, h, progress, barColor, label) {
  // 背景
  fill(50, 50, 50, 200);
  stroke(255, 255, 255, 100);
  strokeWeight(1);
  rect(x, y, w, h, h/2);

  // 进度
  fill(barColor);
  noStroke();
  rect(x + 2, y + 2, (w - 4) * progress, h - 4, (h-4)/2);

  // 标签
  fill(255);
  textAlign(CENTER, CENTER);
  textSize(12);
  text(label, x + w/2, y + h/2);
}

// 绘制连击显示 - 增强版
function drawComboDisplay() {
  if (combo >= 3) {
    let comboLevel = getComboLevel(combo);
    let comboText = `🔥 ${comboLevel.name}连击 ${combo}x`;
    let scale = 1 + sin(millis() * 0.01) * 0.1 * (comboLevel.multiplier - 1);

    push();
    translate(width/2, height - 120);
    scale(scale);

    // 文字阴影
    fill(0, 0, 0, 150);
    textAlign(CENTER, CENTER);
    textSize(20 + comboLevel.multiplier * 2);
    text(comboText, 3, 3);

    // 主文字
    fill(comboLevel.color);
    text(comboText, 0, 0);
    
    // 显示分数倍数
    if (comboLevel.multiplier > 1) {
      textSize(14);
      fill(255, 255, 100);
      text(`分数 x${comboLevel.multiplier.toFixed(1)}`, 0, 25);
    }

    pop();
  }
}

// 绘制激活效果显示
function drawActiveEffects() {
  if (activeEffect.type && millis() < activeEffect.until) {
    let timeLeft = (activeEffect.until - millis()) / 1000;
    let effectText = "";
    let effectColor = color(255, 255, 255);

    switch(activeEffect.type) {
      case 'double':
        effectText = "💰 双倍分数";
        effectColor = color(255, 215, 0);
        break;
      case 'mega':
        effectText = "💥 超级粒子";
        effectColor = color(255, 100, 255);
        break;
      case 'slow':
        effectText = "🌙 慢动作";
        effectColor = color(100, 255, 255);
        break;
      case 'burst':
        effectText = "💣 爆炸增强";
        effectColor = color(255, 50, 50);
        break;
      case 'shield':
        effectText = "🛡️ 护盾保护";
        effectColor = color(100, 150, 255);
        break;
    }

    // 使用布局配置的效果面板位置
    let layout = getUILayout();
    let effectPanel = layout.effectPanel;
    let effectX = effectPanel.x;
    drawPanel(effectX, effectPanel.y, effectPanel.w, effectPanel.h, effectColor);

    fill(255);
    textAlign(CENTER, CENTER);
    textSize(14);
    text(effectText, effectX + effectPanel.w/2, effectPanel.y + 20);
    text("剩余: " + Math.ceil(timeLeft) + "s", effectX + effectPanel.w/2, effectPanel.y + 40);
  }
}

// 绘制任务显示
function drawMissionDisplay() {
  if (currentMission) {
    // 使用布局配置的任务面板位置
    let layout = getUILayout();
    let missionPanel = layout.missionPanel;
    let missionX = missionPanel.x;
    drawPanel(missionX, missionPanel.y, missionPanel.w, missionPanel.h, getThemePalette().accent);

    fill(255);
    textAlign(CENTER, CENTER);
    textSize(15);
    text("🎯 " + currentMission.name, missionX + missionPanel.w/2, missionPanel.y + 20);

    // 进度条
    let progress = currentMission.progress / currentMission.target;
    drawProgressBar(missionX + 30, missionPanel.y + 35, missionPanel.w - 60, 20, progress, getThemePalette().accent, "");

    // 进度文字
    textSize(12);
    text(currentMission.progress + "/" + currentMission.target, missionX + missionPanel.w/2, missionPanel.y + 60);
  }
}

// 处理道具点击 - 增强触摸反馈
function handlePowerUpClick(powerUp, index) {
  playPowerUpSound();
  addScreenShake(6);
  addFlash(80);

  // 添加道具收集的触摸反馈
  if (touchControls) {
    triggerTouchVibration('success');
    addTouchRipple(powerUp.pos.x, powerUp.pos.y, powerUp.getGlowColor());
  }

  activatePowerUp(powerUp.type);

  // 道具收集效果
  for (let j = 0; j < 15; j++) { // 从30减少到15
    let p = new Particle(powerUp.pos.x, powerUp.pos.y, powerUp.getGlowColor());
    safeAddParticle(p);
  }

  scorePopups.push(new ScorePopup(powerUp.pos.x, powerUp.pos.y, "POWER!", "special"));
  powerUps.splice(index, 1);
}

// 处理未击中
function handleMiss() {
  missCount++;
  playMissSound();

  // 重置连击
  if (combo > 0) {
    combo = max(0, combo - 1);
    showToast("💔 连击中断！", "miss");
  }

  // 时间攻击模式惩罚
  if (gameMode === "timeattack") {
    timeLeft -= 2; // 减少2秒
    if (timeLeft < 0) timeLeft = 0;
  }

  // 更新任务进度（无失误任务）
  updateMissionProgress("no_miss", missCount);

  // 创建普通烟花效果
  createHeartFirework(mouseX, mouseY);
}

// 处理连击
function handleCombo() {
  if (millis() - comboTimer < 2000 * upgrades.comboTime) {
    combo++;
    if (combo > 10) combo = 10;

    // 连击特殊效果
    if (combo === 5 && !feverMode) {
      feverMode = true;
      feverUntil = millis() + 7000 * upgrades.feverDuration;
      showToast("🌟 狂热模式激活！", "fever");
      addFlash(100);
    }

    if (combo === 10) {
      showToast("🔥 完美连击！超级奖励！", "perfect");
      score += 100;
      addFlash(150);
    }
  } else {
    combo = 1;
  }
  comboTimer = millis();

  // 检查成就
  checkAchievements();
}

// ============= MOUSE AND INPUT EVENTS =============

function mousePressed() {
  if (gameState === "playing") {
    handleMouseClick();
  }
}

function mouseReleased() {
  // 爆炸能力已改为左键双击触发
  // 右键功能已移除以避免与浏览器冲突
}

// 爆炸能力
function activateBurstAbility() {
  burstCooldown = 300; // 5秒冷却

  playPopSound(0.5);
  addScreenShake(10);
  addFlash(120);

  let burstCount = 0;

  // 检查范围内的气球
  for (let i = balloons.length - 1; i >= 0; i--) {
    let d = dist(mouseX, mouseY, balloons[i].pos.x, balloons[i].pos.y);
    if (d < burstRadius * upgrades.burstRadius) {
      handleBalloonHit(balloons[i], i);
      burstCount++;
    }
  }

  // 爆炸视觉效果
  for (let i = 0; i < 25; i++) { // 从50减少到25
    let angle = random(TWO_PI);
    let distance = random(burstRadius * upgrades.burstRadius);
    let x = mouseX + cos(angle) * distance;
    let y = mouseY + sin(angle) * distance;

    let p = new Particle(x, y, color(255, 100, 100));
    p.vel = createVector(cos(angle), sin(angle)).mult(random(2, 8));
    safeAddParticle(p);
  }

  // 爆炸波纹效果
  drawBurstWave(mouseX, mouseY, burstRadius * upgrades.burstRadius);

  if (burstCount > 0) {
    showToast(`💥 爆炸击中 ${burstCount} 个目标！`, "burst");
    score += burstCount * 15;
  } else {
    showToast("💨 爆炸未击中任何目标", "miss");
  }
}

// 绘制爆炸波纹
function drawBurstWave(x, y, radius) {
  push();
  translate(x, y);
  noFill();
  stroke(255, 100, 100, 200);
  strokeWeight(3);

  for (let i = 0; i < 3; i++) {
    let r = radius * (0.3 + i * 0.35);
    ellipse(0, 0, r * 2);
  }

  pop();
}

// ============= GAME STATE FUNCTIONS =============

// 检查关卡是否完成
function checkLevelComplete() {
  if (levelBalloonCount >= levelTarget) {
    gameState = "levelComplete";
    levelCompleteTimer = millis();

    // 关卡完成奖励
    let levelBonus = currentLevel * 100;
    score += levelBonus;
    coins += currentLevel * 10;

    // 庆祝效果（减少粒子数量）
    for (let i = 0; i < 30; i++) { // 从80减少到30
      let p = new HeartParticle(random(width), random(height), color(255, 215, 0));
      safeAddHeartParticle(p);
    }

    for (let i = 0; i < 20; i++) { // 从50减少到20
      let p = new Particle(random(width), random(height), color(255, 215, 0));
      safeAddParticle(p);
    }

    showToast(`🎉 关卡 ${currentLevel} 完成！奖励 ${levelBonus} 分！`, "level");

    // 检查完美关卡成就
    if (missCount === 0) {
      achievements.perfectLevel = true;
      showToast("✨ 完美关卡！无失误通关！", "perfect");
      score += 200;
      coins += 50;
    }

    // 更新任务进度
    updateMissionProgress("complete_level", 1);
  }
}

// 绘制关卡完成界面
function drawLevelCompleteScreen() {
  // 半透明背景
  fill(0, 0, 0, 200);
  rect(0, 0, width, height);

  let palette = getThemePalette();

  // 主面板
  drawPanel(width/2 - 200, height/2 - 150, 400, 300, palette.primary);

  // 关卡完成文字
  fill(255, 215, 0);
  textAlign(CENTER, CENTER);
  textSize(48);
  text("🎉 关卡 " + currentLevel + " 完成！", width/2, height/2 - 80);

  fill(255);
  textSize(24);
  text("奖励分数: +" + (currentLevel * 100), width/2, height/2 - 30);
  text("奖励金币: +" + (currentLevel * 10), width/2, height/2);
  text("总分数: " + score.toLocaleString(), width/2, height/2 + 30);

  // 统计信息
  textSize(16);
  text("击破气球: " + levelBalloonCount, width/2, height/2 + 70);
  text("连击最高: " + combo, width/2, height/2 + 95);

  if (missCount === 0) {
    fill(255, 215, 0);
    text("✨ 完美关卡！无失误！", width/2, height/2 + 120);
  }

  if (currentLevel < totalLevels) {
    fill(255);
    textSize(18);
    text("3秒后进入下一关...", width/2, height/2 + 150);

    // 进度条
    let timeProgress = (millis() - levelCompleteTimer) / 3000;
    drawProgressBar(width/2 - 100, height/2 + 170, 200, 10, timeProgress, palette.accent, "");

    // 3秒后自动进入下一关
    if (millis() - levelCompleteTimer > 3000) {
      nextLevel();
    }
  } else {
    gameState = "gameComplete";
  }
}

// 绘制游戏完成界面
function drawGameCompleteScreen() {
  // 半透明背景
  fill(0, 0, 0, 220);
  rect(0, 0, width, height);

  let palette = getThemePalette();

  // 主面板
  drawPanel(width/2 - 250, height/2 - 200, 500, 400, palette.primary);

  // 游戏完成文字
  fill(255, 100, 255);
  textAlign(CENTER, CENTER);
  textSize(56);
  text("🏆 恭喜通关！", width/2, height/2 - 120);

  fill(255, 215, 0);
  textSize(32);
  text("最终分数: " + score.toLocaleString(), width/2, height/2 - 60);

  fill(255);
  textSize(24);
  text("击破气球: " + balloonsPoppedCount, width/2, height/2 - 20);
  text("获得金币: " + coins, width/2, height/2 + 10);

  let playTime = Math.floor((millis() - gameStartTime) / 1000);
  let minutes = Math.floor(playTime / 60);
  let seconds = playTime % 60;
  text(`游戏时间: ${minutes}:${seconds.toString().padStart(2, '0')}`, width/2, height/2 + 40);

  // 成就统计
  let achievementCount = Object.values(achievements).filter(a => a).length;
  text(`解锁成就: ${achievementCount}/${Object.keys(achievements).length}`, width/2, height/2 + 70);

  // 评价
  fill(255, 215, 0);
  textSize(28);
  let rating = "";
  if (score >= 5000) rating = "🌟 传奇大师！";
  else if (score >= 3000) rating = "⭐ 烟花专家！";
  else if (score >= 1500) rating = "🎯 熟练玩家！";
  else rating = "🎈 气球新手！";

  text(rating, width/2, height/2 + 110);

  fill(255);
  textSize(18);
  text("按 R 键重新开始游戏", width/2, height/2 + 150);
  text("按 ESC 键返回主菜单", width/2, height/2 + 175);
}

// 绘制暂停界面
function drawPauseScreen() {
  fill(0, 0, 0, 180);
  rect(0, 0, width, height);

  let palette = getThemePalette();
  drawPanel(width/2 - 150, height/2 - 100, 300, 200, palette.secondary);

  fill(255);
  textAlign(CENTER, CENTER);
  textSize(36);
  text("⏸️ 游戏暂停", width/2, height/2 - 40);

  textSize(18);
  text("按 P 键继续游戏", width/2, height/2 + 10);
  text("按 R 键重新开始", width/2, height/2 + 40);
  text("按 ESC 键退出", width/2, height/2 + 70);
}

// 进入下一关
function nextLevel() {
  currentLevel++;
  levelBalloonCount = 0;
  levelTarget = 5 + Math.floor(currentLevel * 1.5); // 每关需要击破的气球数递增
  gameState = "playing";
  bossMode = false; // 修复：确保Boss模式在新关卡开始时被重置
  bossHealth = 0; // 同时重置Boss血量
  maxBossHealth = 0; // 重置最大Boss血量
  missCount = 0; // 重置失误计数

  // 清除所有游戏对象（优化内存清理）
  balloons.length = 0;
  particles.length = 0;
  heartParticles.length = 0;
  scorePopups.length = 0;

  // 生成新任务
  generateNewMission();

  showToast(`🚀 进入关卡 ${currentLevel}！`, "level");

  // 保存进度
  saveGameData();
}

// ============= UTILITY FUNCTIONS =============

// 绘制爱心形状 - 增强版
function drawHeart(x, y, size) {
  push();
  translate(x, y);
  scale(size);

  // 使用主题系统
  let heartColor = getThemeColor(color(255, 50, 100, 180));
  let strokeColor = getThemeColor(color(255, 100, 150, 200));

  // 发光效果
  push();
  blendMode(ADD);
  fill(red(heartColor), green(heartColor), blue(heartColor), 50);
  noStroke();
  beginShape();
  for (let i = 0; i < TWO_PI; i += 0.05) {
    let px = 16 * pow(sin(i), 3) * 1.2;
    let py = -(13 * cos(i) - 5 * cos(2*i) - 2 * cos(3*i) - cos(4*i)) * 1.2;
    vertex(px, py);
  }
  endShape(CLOSE);
  pop();

  // 主爱心
  fill(heartColor);
  stroke(strokeColor);
  strokeWeight(2);

  beginShape();
  for (let i = 0; i < TWO_PI; i += 0.05) {
    let px = 16 * pow(sin(i), 3);
    let py = -(13 * cos(i) - 5 * cos(2*i) - 2 * cos(3*i) - cos(4*i));
    vertex(px, py);
  }
  endShape(CLOSE);

  // 高光
  fill(255, 255, 255, 150);
  noStroke();
  ellipse(-8, -8, 6, 6);

  pop();
}

// 智能爱心烟花创建 - 根据性能自适应
function createHeartFirework(x, y) {
  // 添加临时爱心显示
  heartsToDraw.push({
    x: x,
    y: y,
    size: 3 + combo * 0.1,
    lifetime: 30,
    isAchievement: false
  });

  // 根据性能等级调整粒子数量
  let settings = PERFORMANCE_SETTINGS[currentPerformanceLevel];
  let baseCount = Math.floor(60 * settings.effectQuality);
  let particleCount = Math.min(baseCount + combo * 10, baseCount * 2);

  if (feverMode) particleCount = Math.floor(particleCount * 1.3);
  if (activeEffect.type === 'mega' && millis() < activeEffect.until) {
    particleCount = Math.floor(particleCount * 1.5);
  }

  // 确保不超过性能限制
  particleCount = Math.min(particleCount, settings.maxHeartParticles * 0.6);

  for (let i = 0; i < particleCount; i++) {
    let p = new HeartParticle(x, y);
    if (activeEffect.type === 'mega' && millis() < activeEffect.until) {
      p.r *= 1.3; // 减少放大倍数以提升性能
    }
    safeAddHeartParticle(p);
  }

  // 装饰粒子也要根据性能调整
  let decorativeCount = Math.floor(15 * settings.effectQuality);
  for (let i = 0; i < decorativeCount; i++) {
    let p = new Particle(x, y);
    p.vel.mult(0.4);
    safeAddParticle(p);
  }

  // 计算分数逻辑保持不变
  let baseScore = 10;
  let comboBonus = combo * 2;
  let feverBonus = feverMode ? 10 : 0;
  let effectBonus = (activeEffect.type === 'double' && millis() < activeEffect.until) ? baseScore + comboBonus : 0;

  let totalScore = baseScore + comboBonus + feverBonus + effectBonus;
  score += totalScore;

  scorePopups.push(new ScorePopup(x, y, totalScore, combo >= 5 ? "combo" : "normal"));
}

// ============= POWER-UP AND ACHIEVEMENT SYSTEMS =============

// 道具激活函数 - 增强版
function activatePowerUp(type) {
  let duration = 12000; // 12秒持续时间
  activeEffect = { type: type, until: millis() + duration };

  switch(type) {
    case 'double':
      showToast("💰 双倍分数激活！12秒内分数翻倍！", "powerup");
      break;
    case 'mega':
      showToast("💥 超级粒子激活！12秒内粒子效果翻倍！", "powerup");
      break;
    case 'slow':
      // 修复：使用游戏状态管理而不是setTimeout
      if (gravity && !powerUpEffects.slowMotion.active) {
        powerUpEffects.slowMotion.originalGravity = gravity.copy();
        powerUpEffects.slowMotion.active = true;
        powerUpEffects.slowMotion.until = millis() + duration;
        gravity.mult(0.2);
        showToast("🌙 慢动作激活！12秒内粒子飘得更久！", "powerup");
      }
      break;
    case 'burst':
      // 修复：使用游戏状态管理而不是setTimeout
      if (!powerUpEffects.burstEnhance.active) {
        powerUpEffects.burstEnhance.originalRadius = burstRadius;
        powerUpEffects.burstEnhance.active = true;
        powerUpEffects.burstEnhance.until = millis() + duration;
        burstRadius *= 1.5;
        showToast("💣 爆炸增强激活！12秒内爆炸范围增大！", "powerup");
      }
      break;
    case 'shield':
      showToast("🛡️ 护盾激活！12秒内免疫失误惩罚！", "powerup");
      break;
  }

  updateMissionProgress("collect_powerup", 1);
}

// 成就系统函数 - 增强版
function checkAchievements() {
  // 首次点击成就
  if (!achievements.firstClick && score >= 10) {
    achievements.firstClick = true;
    showToast("🌟 第一次点击！新手玩家达成！", "achievement");
    coins += 10;
  }

  // 连击成就
  if (!achievements.combo3 && combo >= 3) {
    achievements.combo3 = true;
    showToast("🔥 三连击！连击新手！", "achievement");
    coins += 20;
  }

  if (!achievements.combo5 && combo >= 5) {
    achievements.combo5 = true;
    showToast("🌟 五连击！连击大师！", "achievement");
    coins += 50;
  }

  if (!achievements.combo10 && combo >= 10) {
    achievements.combo10 = true;
    showToast("⚡ 十连击！传奇玩家！", "achievement");
    coins += 100;
  }

  // 分数成就
  if (!achievements.score100 && score >= 100) {
    achievements.score100 = true;
    showToast("💯 分数破百！人气王！", "achievement");
    coins += 30;
  }

  if (!achievements.score500 && score >= 500) {
    achievements.score500 = true;
    showToast("🏆 分数破500！烟花大师！", "achievement");
    coins += 75;
  }

  if (!achievements.score1000 && score >= 1000) {
    achievements.score1000 = true;
    showToast("👑 分数破千！烟花之王！", "achievement");
    coins += 150;
  }

  // 气球射手成就
  if (!achievements.balloonPopper && balloonsPoppedCount >= 50) {
    achievements.balloonPopper = true;
    showToast("🎈 气球射手！击破50个气球！", "achievement");
    coins += 60;
  }

  // 狂热大师成就
  if (!achievements.feverMaster && feverMode) {
    achievements.feverMaster = true;
    showToast("🔥 狂热大师！进入狂热模式！", "achievement");
    coins += 40;
  }

  // Boss杀手成就已移动到handleBossDefeat函数中正确处理

  // 神枪手成就（连续击中无失误）
  if (!achievements.sharpShooter && balloonsPoppedCount >= 20 && missCount === 0) {
    achievements.sharpShooter = true;
    showToast("🎯 神枪手！20次击中无失误！", "achievement");
    coins += 80;
  }
}

// 简化的Toast位置管理 - 防止乱飞
function updateToastPositions() {
  // 暂时禁用动态位置调整，让每个Toast保持创建时的位置
  // 这样可以避免Toast之间相互干扰导致的位置混乱
}

// 计算安全的Toast基础Y位置 - 优化版
function getSafeToastBaseY() {
  // 默认位置
  let baseY = 200;

  try {
    // 获取所有活跃Toast的当前Y位置
    let activeToasts = toasts.filter(t => t.lifetime > 0);
    let existingHeights = activeToasts.map(t => t.currentY);

    // 基础间距
    let toastSpacing = 60;

    // 计算下一个Toast的Y位置
    if (existingHeights.length > 0) {
      baseY = Math.max(...existingHeights) + toastSpacing;
    } else {
      // 没有活跃Toast时，使用智能位置
      baseY = Math.min(height * 0.25, 200);
    }

    // 确保不会超出屏幕顶部和底部
    let safeTopMargin = 100;
    let safeBottomMargin = 150; // 避开底部说明面板

    baseY = Math.max(safeTopMargin, Math.min(baseY, height - safeBottomMargin));

    // 防止Toast重叠：检查是否有其他Toast在附近
    for (let toastY of existingHeights) {
      if (Math.abs(baseY - toastY) < toastSpacing * 0.8) {
        baseY = toastY + toastSpacing;
      }
    }

    // 最终边界检查
    baseY = Math.max(safeTopMargin, Math.min(baseY, height - safeBottomMargin));

  } catch (e) {
    console.warn('Toast positioning error:', e);
    baseY = 200; // 出错时使用安全默认值
  }

  return Math.round(isNaN(baseY) || !isFinite(baseY) ? 200 : baseY); // 修复：确保baseY是有效有限数值
}

// 内存管理系统 - 增强版
let memoryCleanupCounter = 0;
const MEMORY_CLEANUP_INTERVAL = 300; // 每300帧清理一次
let lastGCHint = 0;

// 安全添加粒子，防止数量过多导致死机
function safeAddParticle(particle) {
  // 验证粒子有效性
  if (!isValidParticle(particle)) {
    console.warn('Invalid particle rejected:', particle);
    return false;
  }

  // 修复：使用合理的粒子数量限制，基于全局常量的百分比
  const SAFE_PARTICLE_LIMIT = Math.floor(MAX_PARTICLES * 0.6); // 使用全局常量的60%

  if (particles.length < SAFE_PARTICLE_LIMIT) {
    particles.push(particle);
    return true;
  } else {
    // 如果粒子太多，直接丢弃新粒子
    return false;
  }
}

function safeAddHeartParticle(particle) {
  // 验证粒子有效性
  if (!isValidParticle(particle)) {
    console.warn('Invalid heart particle rejected:', particle);
    return false;
  }

  // 修复：使用合理的心形粒子数量限制，基于全局常量的百分比
  const SAFE_HEART_PARTICLE_LIMIT = Math.floor(MAX_HEART_PARTICLES * 0.5); // 使用全局常量的50%

  if (heartParticles.length < SAFE_HEART_PARTICLE_LIMIT) {
    heartParticles.push(particle);
    return true;
  } else {
    // 如果粒子太多，直接丢弃新粒子
    return false;
  }
}

// 修复：优化粒子验证函数，调整边界范围
function isValidParticle(particle) {
  if (!particle || !particle.pos) return false;

  let x = particle.pos.x;
  let y = particle.pos.y;

  // 基本数值检查
  if (typeof x !== 'number' || typeof y !== 'number' || isNaN(x) || isNaN(y)) {
    return false;
  }

  // 修复：根据粒子类型调整边界范围，减少内存浪费
  let buffer = 50; // 默认缓冲区
  try {
    // 使用更可靠的属性检查而非类型名称
    if (particle.hasTrail !== undefined || particle.createHeartVelocity !== undefined) {
      buffer = 100; // 爱心粒子需要更大缓冲区
    }
  } catch (e) {
    // 如果属性检查失败，使用默认缓冲区
  }

  return x >= -buffer &&
         x <= width + buffer &&
         y >= -buffer &&
         y <= height + buffer;
}

// 深度内存清理 - 增强版
function deepMemoryCleanup() {
  memoryCleanupCounter++;

  // 定期深度清理
  if (memoryCleanupCounter % MEMORY_CLEANUP_INTERVAL === 0) {
    let before = {
      particles: particles.length,
      hearts: heartParticles.length,
      balloons: balloons.length,
      powerUps: powerUps.length,
      toasts: toasts.length,
      popups: scorePopups.length
    };
    
    // 修复：使用splice而不是filter来提高性能
    // 清理无效粒子 - 从后往前遍历避免索引问题
    for (let i = particles.length - 1; i >= 0; i--) {
      if (!isValidParticle(particles[i]) || particles[i].isFinished()) {
        particles.splice(i, 1);
      }
    }
    for (let i = heartParticles.length - 1; i >= 0; i--) {
      if (!isValidParticle(heartParticles[i]) || heartParticles[i].isFinished()) {
        heartParticles.splice(i, 1);
      }
    }

    // 清理无效气球
    balloons = balloons.filter(b => b && b.pos && !b.isOffScreen());

    // 清理无效道具
    powerUps = powerUps.filter(p => p && p.pos && !p.isFinished());

    // 清理无效Toast并同步更新Map
    let validToasts = toasts.filter(t => t && t.lifetime > 0);
    if (validToasts.length !== toasts.length) {
      // 清理Map中的无效条目
      activeToastMessages.clear();
      validToasts.forEach(toast => {
        if (toast.message) {
          activeToastMessages.set(toast.message, toast);
        }
      });
    }
    toasts = validToasts;

    // 清理无效分数弹出
    scorePopups = scorePopups.filter(s => s && s.lifetime > 0);

    // 清理无效爱心绘制
    heartsToDraw = heartsToDraw.filter(h => h && h.lifetime > 0);
    
    // 清理已结束的特殊事件
    specialEvents = specialEvents.filter(e => e && e.active);
    
    // 清理流星
    shootingStars = shootingStars.filter(s => s && !s.isFinished());

    let after = {
      particles: particles.length,
      hearts: heartParticles.length,
      balloons: balloons.length,
      powerUps: powerUps.length,
      toasts: toasts.length,
      popups: scorePopups.length
    };
    
    let cleaned = Object.keys(before).some(key => before[key] > after[key]);
    if (cleaned) {
      console.log('Memory cleanup:', before, '->', after);
    }
  }

  // 垃圾回收提示（仅开发环境）
  if (memoryCleanupCounter % (MEMORY_CLEANUP_INTERVAL * 10) === 0) {
    let currentTime = millis();
    if (currentTime - lastGCHint > 30000) { // 30秒间隔
      // 在浏览器环境中建议垃圾回收
      if (typeof window !== 'undefined' && window.gc) {
        setTimeout(() => window.gc(), 100);
      }
      lastGCHint = currentTime;
    }
  }

  // 紧急内存清理 - 防止卡死
  let totalObjects = particles.length + heartParticles.length + balloons.length + powerUps.length + toasts.length + scorePopups.length;
  if (totalObjects > 1000) {
    console.warn('Emergency cleanup triggered! Total objects:', totalObjects);
    
    // 修复：使用高效的slice操作，避免性能问题
    // 保留最新的粒子，删除最旧的
    let targetParticleCount = Math.floor(particles.length * 0.5);
    if (particles.length > targetParticleCount) {
      particles = particles.slice(-targetParticleCount);
    }

    let targetHeartCount = Math.floor(heartParticles.length * 0.5);
    if (heartParticles.length > targetHeartCount) {
      heartParticles = heartParticles.slice(-targetHeartCount);
    }

    let targetPopupCount = Math.floor(scorePopups.length * 0.3);
    if (scorePopups.length > targetPopupCount) {
      scorePopups = scorePopups.slice(-targetPopupCount);
    }

    if (toasts.length > 3) {
      toasts = toasts.slice(-3);
    }
    
    showToast('⚠️ 紧急清理已启动', 'emergency');
  }
}

// Toast冷却系统 - 性能保护
let lastToastTime = 0;
const TOAST_COOLDOWN = 1000; // 增加到1秒冷却时间
let activeToastMessages = new Map(); // 跟踪活跃的Toast消息

// 显示提示消息 - 严格的性能保护
function showToast(message, type = "info") {
  // 性能保护：如果游戏卡顿，直接跳过
  if (frameRate() < 30) {
    return;
  }

  // 冷却检查 - 防止Toast过于频繁
  let currentTime = millis();
  if (currentTime - lastToastTime < TOAST_COOLDOWN) {
    return; // 跳过所有Toast
  }

  // 严格限制同时显示的Toast数量
  const MAX_TOASTS = 2; // 减少到2个

  // 安全清理Toast数组
  try {
    toasts = toasts.filter(t => t && t.lifetime > 0);

    // 如果Toast太多，移除最老的
    while (toasts.length >= MAX_TOASTS) {
      toasts.shift();
    }
  } catch (e) {
    // 如果出错，清空数组
    toasts = [];
  }

  // 防止重复消息 - 使用Map提高性能
  // 清理过期的Toast引用
  for (let [msg, toast] of activeToastMessages.entries()) {
    if (!toast || toast.lifetime <= 0) {
      activeToastMessages.delete(msg);
    }
  }

  if (activeToastMessages.has(message)) {
    let existingToast = activeToastMessages.get(message);
    if (existingToast && existingToast.lifetime > 30) {
      // 刷新现有Toast的生命周期
      existingToast.lifetime = Math.max(existingToast.lifetime, 120);
      return;
    }
  }

  let newToast = new Toast(message, type);
  toasts.push(newToast);
  activeToastMessages.set(message, newToast);
  lastToastTime = currentTime;
}

// 显示特殊的欢迎消息，位置在屏幕中央上方
function showWelcomeToast(message) {
  let welcomeToast = new Toast(message, "welcome");
  // 特殊位置：屏幕中央上方
  welcomeToast.x = width / 2 - welcomeToast.width / 2; // 基于实际宽度居中显示
  welcomeToast.targetX = width / 2 - welcomeToast.width / 2;
  welcomeToast.currentY = 120; // 屏幕上方
  welcomeToast.targetY = 120;
  welcomeToast.lifetime = 300; // 显示更长时间（5秒）
  welcomeToast.maxLifetime = 300;
  toasts.push(welcomeToast);
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);

  // 简化的响应式更新
  try {
    updateResponsiveUI();
  } catch (e) {
    console.warn("响应式UI更新失败:", e);
  }

  // 重新定位星星
  if (stars && stars.length > 0) {
    for (let star of stars) {
      if (star && star.pos) {
        if (star.pos.x > width) star.pos.x = random(width);
        if (star.pos.y > height) star.pos.y = random(height);
      }
    }
  }

  // 清除UI布局缓存，强制重新计算
  if (typeof UI_LAYOUT_CACHE !== 'undefined') {
    UI_LAYOUT_CACHE = null;
  }

  // 简化Toast重新计算，避免复杂操作
  if (toasts && toasts.length > 0) {
    // 过滤出有效的Toast对象
    toasts = toasts.filter(toast => {
      if (!toast || toast.lifetime <= 0) return false;

      try {
        // 使用Toast类中已有的方法重新计算尺寸
        toast.width = toast.getWidth();
        toast.height = toast.getHeight();
        return true;
      } catch (e) {
        console.warn("Toast更新失败，移除无效Toast:", e);
        // 移除有问题的Toast而不是尝试修复
        return false;
      }
    });
  }
}

// ============= MISSION SYSTEM =============

// 生成新任务
function generateNewMission() {
  let missions = [
    { name: "连击达人", type: "combo", target: 5, progress: 0, reward: 50 },
    { name: "气球猎手", type: "pop_balloons", target: 10, progress: 0, reward: 30 },
    { name: "分数冲刺", type: "score", target: 500, progress: 0, reward: 40 },
    { name: "道具收集者", type: "collect_powerup", target: 3, progress: 0, reward: 60 },
    { name: "完美射手", type: "no_miss", target: 15, progress: 0, reward: 80 },
    { name: "关卡征服者", type: "complete_level", target: 1, progress: 0, reward: 100 }
  ];

  currentMission = missions[Math.floor(Math.random() * missions.length)];
}

// 更新任务进度
function updateMissionProgress(type, amount) {
  if (!currentMission) return;

  // 处理不同类型的任务
  if (currentMission.type === type) {
    if (type === "no_miss") {
      // 无失误任务：每次击中且当前无失误时增加进度
      if (amount > 0 && missCount === 0) {
        currentMission.progress = min(currentMission.progress + 1, currentMission.target);
      } else if (missCount > 0) {
        // 有失误时重置进度
        currentMission.progress = 0;
      }
    } else {
      currentMission.progress = min(currentMission.progress + amount, currentMission.target);
    }
  }

  if (currentMission.progress >= currentMission.target) {
    // 任务完成
    coins += currentMission.reward;
    showToast(`🎯 任务完成！获得 ${currentMission.reward} 金币！`, "mission");

    // 生成新任务
    setTimeout(() => {
      generateNewMission();
    }, 2000);
  }
}

// ============= DATA PERSISTENCE =============

// 安全的localStorage操作函数
function safeLocalStorageSetItem(key, value) {
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (e) {
    console.warn('Failed to save to localStorage:', e);
    showToast("⚠️ 数据保存失败", "warning");
    return false;
  }
}

function safeLocalStorageGetItem(key, defaultValue = null) {
  try {
    return localStorage.getItem(key) || defaultValue;
  } catch (e) {
    console.warn('Failed to read from localStorage:', e);
    return defaultValue;
  }
}

// 保存游戏数据
function saveGameData() {
  try {
    let currentHighScore = parseInt(safeLocalStorageGetItem('heartFireworks_highScore', '0'), 10);
    let gameData = {
      coins: coins,
      achievements: achievements,
      upgrades: upgrades,
      highScore: Math.max(score, currentHighScore),
      currentLevel: currentLevel,
      totalBalloons: balloonsPoppedCount
    };

    safeLocalStorageSetItem('heartFireworks_gameData', JSON.stringify(gameData));
    safeLocalStorageSetItem('heartFireworks_highScore', gameData.highScore.toString());
  } catch (e) {
    console.warn('Error saving game data:', e);
    showToast("⚠️ 游戏数据保存失败", "warning");
  }
}

// 加载游戏数据
function loadGameData() {
  try {
    let savedData = safeLocalStorageGetItem('heartFireworks_gameData');
    if (savedData) {
      let gameData = JSON.parse(savedData);
      // 安全地加载数据，使用默认值防止undefined错误
      coins = typeof gameData.coins === 'number' ? gameData.coins : 0;
      achievements = gameData.achievements ? { ...achievements, ...gameData.achievements } : achievements;
      upgrades = gameData.upgrades ? { ...upgrades, ...gameData.upgrades } : upgrades;
    }
  } catch (e) {
    console.warn('Failed to load game data:', e);
    showToast("⚠️ 游戏数据加载失败，使用默认设置", "warning");
  }
}

// ============= KEYBOARD CONTROLS =============

function keyPressed() {
  if (key === ' ') { // 空格键 - 清除所有粒子或暂停
    if (gameState === "playing") {
      particles = [];
      heartParticles = [];
      heartsToDraw = [];
      playClickSound(400, 0.2);
    }
  } else if (key === 'r' || key === 'R') { // R键 - 重新开始游戏或随机烟花
    if (gameState === "gameComplete" || gameState === "paused") {
      restartGame();
    } else if (gameState === "playing") {
      let x = random(width);
      let y = random(height * 0.7);
      createHeartFirework(x, y);
      playClickSound(600, 0.15);
    }
  } else if (key === 'c' || key === 'C') { // C键 - 屏幕中央发射爱心烟花
    if (gameState === "playing") {
      createHeartFirework(width/2, height/2);
      playClickSound(700, 0.15);
    }
  } else if (key === 'p' || key === 'P') { // P键 - 暂停/继续
    if (gameState === "playing") {
      gameState = "paused";
      showToast("⏸️ 游戏已暂停", "info");
    } else if (gameState === "paused") {
      gameState = "playing";
      showToast("▶️ 游戏继续", "info");
    }
    playClickSound(500, 0.2);
  } else if (key === 't' || key === 'T') { // T键 - 切换主题
    currentTheme = (currentTheme + 1) % 5;
    let themeNames = ["粉色主题", "彩虹主题", "金色主题", "霓虹主题", "火焰主题"];
    showToast("🎨 切换到" + themeNames[currentTheme] + "！", "theme");
    playClickSound(900, 0.2);
  } else if (key === 'm' || key === 'M') { // M键 - 音效开关
    soundEnabled = !soundEnabled;
    // 修复：重新启用音频时重置错误计数
    if (soundEnabled) {
      audioErrorCount = 0;
      lastAudioErrorTime = 0;
    }
    showToast(soundEnabled ? "🔊 音效已开启" : "🔇 音效已关闭", "audio");
    saveGameData();
  } else if (key === 'n' || key === 'N') { // N键 - 跳过关卡完成界面
    if (gameState === "levelComplete") {
      if (currentLevel < totalLevels) {
        nextLevel();
      } else {
        gameState = "gameComplete";
      }
    }
  } else if (key === 'h' || key === 'H') { // H键 - 切换UI显示
    showUI = !showUI;
    showToast(showUI ? "👁️ UI已显示" : "🙈 UI已隐藏", "ui");
  } else if (key === 'q' || key === 'Q') { // Q键 - 跳过教程
    if (tutorialActive) {
      tutorialActive = false;
      tutorialHighlight = null;
      showToast("⏭️ 教程已跳过", "tutorial");
    }
  } else if (key === 'u' || key === 'U') { // U键 - 重新开始教程
    if (!tutorialActive) {
      restartTutorial();
    }
  } else if (key === 'p' || key === 'P') { // P键 - 练习模式
    togglePracticeMode();
  } else if (key === 'i' || key === 'I') { // I键 - 切换说明面板
    // 说明面板的切换逻辑在HTML中处理
    showToast("📖 说明面板已切换", "info");
  } else if (key === 'd' || key === 'D') { // D键 - 调试信息
    console.log("UI Layout Debug:");
    console.log("Screen size:", width, "x", height);
    let layout = getUILayout();
    console.log("Right panel:", layout.rightPanel.x, layout.rightPanel.y, "to", layout.rightPanel.x + layout.rightPanel.w, layout.rightPanel.y + layout.rightPanel.h);
    console.log("Effect panel:", layout.effectPanel.x, layout.effectPanel.y, "to", layout.effectPanel.x + layout.effectPanel.w, layout.effectPanel.y + layout.effectPanel.h);
    console.log("Mission panel:", layout.missionPanel.x, layout.missionPanel.y, "to", layout.missionPanel.x + layout.missionPanel.w, layout.missionPanel.y + layout.missionPanel.h);
    console.log("Safe Toast base Y:", getSafeToastBaseY());
    console.log("Active toasts:", toasts.length, toasts.map(t => `Y:${Math.round(t.currentY)}`));
    console.log("Mission panel:", currentMission ? "visible" : "hidden");
    console.log("Effect panel:", (activeEffect.type && millis() < activeEffect.until) ? "visible" : "hidden");
    showToast("🔧 调试信息已输出到控制台", "debug");
  } else if (keyCode === ESCAPE) { // ESC键 - 退出到主菜单
    if (gameState === "paused" || gameState === "gameComplete") {
      // 这里可以添加主菜单逻辑
      showToast("🏠 返回主菜单功能待实现", "info");
    }
  }
}

// ============= TUTORIAL FUNCTIONS =============

// 重新开始教程
function restartTutorial() {
  tutorialStep = 0;
  tutorialActive = true;
  tutorialTimer = 0;
  tutorialHighlight = null;

  showToast("🎓 教程重新开始！", "tutorial");

  // 显示第一步教程
  setTimeout(() => {
    if (tutorialActive && TUTORIAL_STEPS.length > 0) {
      showToast(TUTORIAL_STEPS[0].message, "tutorial");
      if (TUTORIAL_STEPS[0].highlight) {
        createTutorialHighlight(TUTORIAL_STEPS[0]);
      }
    }
  }, 1000);
}

// 切换练习模式
function togglePracticeMode() {
  practiceMode = !practiceMode;

  if (practiceMode) {
    showToast("🎯 练习模式已开启！无限生命，慢速气球", "practice");

    // 练习模式设置
    maxMisses = 999; // 无限生命

    // 生成练习气球
    spawnPracticeBalloons();
  } else {
    showToast("🎮 练习模式已关闭", "info");
    maxMisses = 3; // 恢复正常生命

    // 清除练习气球
    balloons = balloons.filter(b => !b.isPractice);
  }
}

// 生成练习气球
function spawnPracticeBalloons() {
  // 清除现有气球
  balloons = [];

  // 生成几个慢速练习气球
  for (let i = 0; i < 3; i++) {
    let balloon = new Balloon("normal");
    balloon.pos.x = width * (0.2 + i * 0.3);
    balloon.pos.y = height * 0.7;
    balloon.vel.mult(0.3); // 慢速
    balloon.isPractice = true;
    balloons.push(balloon);
  }
}

// ============= GAME RESTART =============

// 重新开始游戏
function restartGame() {
  // 安全地保存最高分
  try {
    let currentHighScore = parseInt(safeLocalStorageGetItem('heartFireworks_highScore', '0'), 10);
    let highScore = Math.max(score, currentHighScore);
    safeLocalStorageSetItem('heartFireworks_highScore', highScore.toString());
  } catch (e) {
    console.warn('Failed to save high score:', e);
  }

  // 重置游戏状态
  score = 0;
  combo = 0;
  comboTimer = 0;
  balloonsPoppedCount = 0;
  missCount = 0;
  currentLevel = 1;
  levelTarget = 5;
  levelBalloonCount = 0;
  gameState = "playing";
  bossMode = false;
  bossHealth = 0;
  feverMode = false;
  activeEffect = { type: null, until: 0 };
  burstCooldown = 0;

  // 重置道具效果
  powerUpEffects.slowMotion.active = false;
  powerUpEffects.slowMotion.originalGravity = null;
  powerUpEffects.burstEnhance.active = false;
  powerUpEffects.burstEnhance.originalRadius = 100;
  burstRadius = 100; // 重置爆炸半径

  // 重置双击检测
  clickCount = 0;
  doubleClickTime = 0;

  gameStartTime = millis();

  // 重置视觉效果
  cameraShakeX = 0;
  cameraShakeY = 0;
  hitStopUntil = 0;
  flashOverlay = 0;

  // 修复：正确清理包含复杂对象的数组，确保垃圾回收
  // 对于包含对象的数组，先清空对象引用再重置数组
  particles.length = 0;
  heartParticles.length = 0;
  scorePopups.length = 0;

  // 清理Toast时同步清理Map
  toasts.length = 0;
  activeToastMessages.clear();

  balloons.length = 0;
  powerUps.length = 0;
  heartsToDraw.length = 0;
  shootingStars.length = 0;

  // 重新初始化星星（优化内存清理）
  stars.length = 0;
  let settings = PERFORMANCE_SETTINGS[currentPerformanceLevel];
  for (let layer = 1; layer <= 3; layer++) {
    let starCount = settings.starCount[layer - 1];
    for (let i = 0; i < starCount; i++) {
      stars.push(new Star(layer));
    }
  }

  // 生成新任务
  generateNewMission();

  showToast("🔄 游戏重新开始！", "restart");
  playClickSound(300, 0.3);
}

// ============= END OF ENHANCED HEART FIREWORKS GAME =============

// 游戏完整实现完成！
// 新增功能包括：
// - 增强的粒子系统和视觉效果
// - 多种气球类型（护盾、分裂、快速、隐身等）
// - 爆炸能力和冷却系统
// - 任务系统和金币奖励
// - 数据持久化保存
// - 多层视差背景和流星效果
// - 现代化UI设计和动画
// - 增强的音效系统
// - 屏幕震动和击中停顿效果
// - 成就系统和进度追踪
