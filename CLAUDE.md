# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a p5.js interactive particle system project that creates an "爱心烟花闯关游戏" (Heart Fireworks Balloon Pop Game). The project features:

1. Interactive particle effects with heart-shaped fireworks
2. Balloon popping gameplay with multiple balloon types (normal, special, boss)
3. Level progression system with 10 levels
4. Combo system and scoring mechanics
5. Special effects including stars, particles, and visual feedback

## Code Architecture

### Main Components

1. **Particle Classes**:
   - `Particle`: Basic particle with physics
   - `HeartParticle`: Heart-shaped particle with special velocity distribution
   - `Star`: Background twinkling stars

2. **Game Objects**:
   - `Balloon`: Different types (normal, special, boss) with unique properties
   - Game state management (score, combos, levels)

3. **Core Systems**:
   - Particle system with gravity and physics
   - Level progression and game state management
   - User interaction handling (mouse clicks, keyboard input)
   - Visual effects and animations

### File Structure

- `index.html`: Main game interface with instructions
- `sketch_simple.js`: Main game implementation with balloon popping and level system
- `sketch.js`: Alternative implementation with additional features (power-ups, themes, achievements)
- `test.html`: Simple test page

## Common Development Tasks

### Building and Running

1. Open `index.html` in a web browser to run the main game
2. Open `test.html` for a simplified version
3. No build process required - pure client-side JavaScript

### Development Commands

```bash
# To test locally, you can use Python's built-in server:
python -m http.server 8000

# Or Node.js http-server:
npx http-server

# Then open http://localhost:8000 in your browser
```

### Testing

Manual testing through browser interaction is the primary testing method. Key behaviors to verify:
1. Mouse clicks create heart particle effects
2. Balloons spawn and move correctly
3. Balloon popping works and awards points
4. Level progression functions properly
5. Keyboard controls work (R, C, S, N, Space)

## Architecture Guidelines

1. All code follows p5.js conventions and structure
2. Classes are used for organizing game objects
3. Global variables manage game state
4. Canvas-based rendering with particle effects
5. Event-driven interaction model (mouse/keyboard)

## Key Implementation Details

1. **Particle System**: Custom particle classes with physics (velocity, acceleration, gravity)
2. **Heart Shape Generation**: Parametric equations for heart shapes in particle velocity
3. **Level System**: Progressive difficulty with increasing balloon count and speed
4. **Special Balloons**: Different types with unique properties and scoring
5. **Visual Effects**: Background stars, particle fading, combo displays