<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>💗 爱心烟花游戏</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>

  <!-- 现代化中文字体系统 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* ========== 星河梦境设计系统 ========== */

    :root {
      /* 主色彩系统 - 深空渐变 */
      --primary-gradient: linear-gradient(135deg, #0c0c1e 0%, #1a1a3a 25%, #2d1b69 50%, #1e3c72 75%, #2a5298 100%);
      --accent-gradient: linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
      --glass-bg: rgba(255, 255, 255, 0.08);
      --glass-border: rgba(255, 255, 255, 0.18);
      --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);

      /* 文字颜色系统 */
      --text-primary: rgba(255, 255, 255, 0.95);
      --text-secondary: rgba(255, 255, 255, 0.75);
      --text-muted: rgba(255, 255, 255, 0.55);

      /* 间距系统 */
      --space-xs: 0.25rem;
      --space-sm: 0.5rem;
      --space-md: 1rem;
      --space-lg: 1.5rem;
      --space-xl: 2rem;
      --space-2xl: 3rem;

      /* 圆角系统 */
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --radius-xl: 24px;
      --radius-full: 9999px;

      /* 字体系统 */
      --font-primary: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
      --font-weight-light: 300;
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      --font-weight-bold: 700;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: var(--font-primary);
      background: linear-gradient(135deg, #0b1020 0%, #1a1f3b 100%);
      color: var(--text-primary);
    }

    /* ========== 优化说明面板 - 无滚动条设计 ========== */
    .instructions {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.92);
      backdrop-filter: blur(20px);
      color: white;
      padding: 20px;
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      font-size: 13px;
      z-index: 1000;
      width: 320px;
      max-width: calc(100vw - 40px);
      height: auto;
      max-height: calc(100vh - 40px);
      overflow: hidden; /* 禁用滚动条 */
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
      transition: all 0.5s ease;
      opacity: 0.95;
    }

    .instructions:hover {
      transform: translateX(-8px) translateY(-4px);
      box-shadow:
        var(--glass-shadow),
        0 32px 64px rgba(31, 38, 135, 0.5);
      opacity: 1;
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
    }

    .instructions h3 {
      margin: 0 0 12px 0;
      color: #ff69b4;
      font-weight: 700;
      text-align: center;
      font-size: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    }

    /* ========== 现代化响应式设计 ========== */
    @media (max-width: 768px) {
      :root {
        --space-xs: 0.2rem;
        --space-sm: 0.4rem;
        --space-md: 0.8rem;
        --space-lg: 1.2rem;
        --space-xl: 1.6rem;
        --space-2xl: 2.4rem;
      }

      .instructions {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        bottom: auto;
        transform: translate(-50%, -50%);
        width: 92vw;
        max-width: 420px;
        max-height: 85vh;
        border-radius: var(--radius-lg);
        padding: var(--space-lg);
        font-size: 13px;
        backdrop-filter: blur(20px) saturate(180%);
      }

      .instructions:hover {
        transform: translate(-50%, -50%) scale(1.02);
      }

      .instructions.hidden {
        transform: translate(-50%, -50%) scale(0.85);
        opacity: 0;
        pointer-events: none;
      }

      .instructions h3 {
        font-size: 18px;
        margin-bottom: var(--space-md);
      }
    }

    @media (max-width: 480px) {
      .instructions {
        width: 95vw;
        padding: var(--space-md);
        font-size: 12px;
        border-radius: var(--radius-md);
      }

      .instructions h3 {
        font-size: 16px;
      }
    }

    /* ========== 现代化收缩状态 ========== */
    @media (min-width: 769px) {
      .instructions.collapsed {
        width: 64px;
        height: 64px;
        padding: 0;
        border-radius: var(--radius-full);
        overflow: hidden;
        cursor: pointer;
        transform: translateX(0);
        background: var(--accent-gradient);
        border: 2px solid rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
      }

      .instructions.collapsed:hover {
        transform: translateX(-8px) scale(1.15);
        box-shadow:
          0 20px 40px rgba(31, 38, 135, 0.6),
          0 0 0 8px rgba(255, 255, 255, 0.1);
      }

      .instructions.collapsed .instructions-content {
        display: none;
      }

      .instructions.collapsed::before {
        content: "✨";
        font-size: 28px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        animation: float 3s ease-in-out infinite;
      }
    }

    @keyframes float {
      0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
      50% { transform: translate(-50%, -50%) translateY(-4px); }
    }

    /* ========== 简洁内容样式 ========== */
    .section {
      margin-bottom: 16px;
      padding: 0;
    }

    .section h4 {
      color: #00e5ff;
      font-size: 14px;
      margin-bottom: 8px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .section h4::before {
      content: '';
      width: 3px;
      height: 14px;
      background: linear-gradient(135deg, #00e5ff, #0099cc);
      border-radius: 2px;
    }

    .section p, .section div {
      color: rgba(255, 255, 255, 0.85);
      line-height: 1.4;
      font-size: 12px;
      margin: 0;
    }

    .controls-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4px;
      margin-bottom: 8px;
    }

    .control-item {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 3px 5px;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 4px;
      font-size: 10px;
    }

    .key {
      background: linear-gradient(135deg, #ff6ec7, #ff9a9e);
      color: white;
      padding: 1px 4px;
      border-radius: 3px;
      font-weight: 600;
      font-size: 9px;
      min-width: 30px;
      text-align: center;
      flex-shrink: 0;
    }

    .feature-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3px;
      margin: 6px 0;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 10px;
      color: #ccc;
      padding: 2px 0;
    }

    .feature-icon {
      font-size: 12px;
      width: 14px;
      text-align: center;
      flex-shrink: 0;
    }

    .info {
      font-size: 12px;
      color: #ddd;
      line-height: 1.5;
      margin-top: 12px;
      padding: 12px;
      background: rgba(255, 105, 180, 0.1);
      border-radius: 10px;
      border-left: 4px solid #ff69b4;
    }

    .hide-tip {
      text-align: center;
      margin-top: 12px;
      padding: 8px;
      background: rgba(0, 229, 255, 0.1);
      border-radius: 8px;
      font-size: 11px;
      color: #00e5ff;
      font-weight: 600;
    }

    .loading {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 24px;
      z-index: 2000;
      text-align: center;
    }

    .loading .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #ff69b4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* ========== 简洁收缩按钮 ========== */
    .collapse-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 24px;
      height: 24px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      z-index: 10;
    }

    .collapse-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }

    @media (max-width: 768px) {
      .collapse-btn {
        display: none;
      }
    }

    .version {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: rgba(255, 255, 255, 0.5);
      font-size: 10px;
      z-index: 1000;
    }
  </style>
</head>
<body>
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <div>加载增强版爱心烟花游戏...</div>
  </div>

  <div class="instructions" id="instructions" style="display: none;">
    <div class="instructions-content">
      <!-- 收缩按钮 -->
      <div class="collapse-btn" id="collapseBtn" onclick="toggleInstructionsCollapse()">
        <span>📖</span>
      </div>

      <h3>💗 爱心烟花游戏</h3>

      <div class="section">
        <h4>🎯 游戏目标</h4>
        <div>点击气球获得分数，连击获得加成</div>
      </div>

    <div class="section">
      <h4>🎮 基础操作</h4>
      <div class="controls-grid">
        <div class="control-item">
          <span class="key">左键</span>
          <span>射击</span>
        </div>
        <div class="control-item">
          <span class="key">双击</span>
          <span>爆炸</span>
        </div>
        <div class="control-item">
          <span class="key">I键</span>
          <span>说明</span>
        </div>
        <div class="control-item">
          <span class="key">空格</span>
          <span>暂停</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h4>🎈 气球类型</h4>
      <div class="feature-list">
        <div class="feature-item">
          <span class="feature-icon">🔵</span>
          <span>普通气球</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">⭐</span>
          <span>特殊气球</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">👹</span>
          <span>Boss气球</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">💥</span>
          <span>分裂气球</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🛡️</span>
          <span>护盾气球</span>
        </div>
      </div>
    </div>

    <div class="section">
      <h4>💡 游戏技巧</h4>
      <div style="font-size: 11px; line-height: 1.3; color: #98fb98;">
        • 连击获得分数加成<br>
        • 收集道具获得能力<br>
        • 双击触发爆炸攻击<br>
        • 完成任务获得奖励
      </div>
    </div>

    <div class="info">
      <strong style="color: #ff69b4;">🎯 开始游戏：</strong>
      <span style="color: #87ceeb;">直接用鼠标左键点击屏幕上的气球开始射击！</span><br>
      <strong style="color: #ff69b4;">💥 特殊技能：</strong>
      <span style="color: #87ceeb;">快速左键双击可触发爆炸攻击，清理大片气球！</span><br>
      <strong style="color: #ff69b4;">🏆 获胜条件：</strong>
      <span style="color: #87ceeb;">击破足够数量的气球，完成关卡任务即可过关！</span>
    </div>

      <div class="hide-tip">
        💡 按 I 键可隐藏/显示此面板
      </div>
    </div> <!-- 关闭 instructions-content -->
  </div>

  <div class="version">Enhanced v2.0</div>

  <script>
    // 页面加载完成后隐藏加载界面
    window.addEventListener('load', function() {
      setTimeout(() => {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('instructions').style.display = 'block';
      }, 1500);
    });

    // 说明面板状态管理
    let instructionsVisible = true;
    let instructionsCollapsed = false;

    // 切换说明面板显示/隐藏
    function toggleInstructions() {
      const instructions = document.getElementById('instructions');
      instructionsVisible = !instructionsVisible;

      if (window.innerWidth <= 768) {
        // 移动端：居中显示/隐藏
        if (instructionsVisible) {
          instructions.classList.remove('hidden');
          instructions.style.display = 'block';
        } else {
          instructions.classList.add('hidden');
          setTimeout(() => {
            instructions.style.display = 'none';
          }, 500);
        }
      } else {
        // 桌面端：右侧滑入/滑出
        if (instructionsVisible) {
          instructions.style.transform = 'translateX(0)';
          instructions.style.opacity = '0.95';
          instructions.style.display = 'block';
        } else {
          instructions.style.transform = 'translateX(100%)';
          instructions.style.opacity = '0';
          setTimeout(() => {
            instructions.style.display = 'none';
          }, 500);
        }
      }
    }

    // 切换说明面板收缩状态（仅桌面端）
    function toggleInstructionsCollapse() {
      if (window.innerWidth <= 768) return;

      const instructions = document.getElementById('instructions');
      instructionsCollapsed = !instructionsCollapsed;

      if (instructionsCollapsed) {
        instructions.classList.add('collapsed');
      } else {
        instructions.classList.remove('collapsed');
      }
    }

    // 键盘控制
    document.addEventListener('keydown', function(e) {
      if (e.key === 'i' || e.key === 'I') {
        toggleInstructions();
      }
    });

    // 响应式处理
    window.addEventListener('resize', function() {
      const instructions = document.getElementById('instructions');

      // 移动端时移除桌面端的收缩状态
      if (window.innerWidth <= 768) {
        instructions.classList.remove('collapsed');
        instructionsCollapsed = false;
      }

      // 重新应用显示状态
      if (instructionsVisible) {
        instructions.style.display = 'block';
        instructions.classList.remove('hidden');
        instructions.style.transform = window.innerWidth <= 768 ? 'translate(-50%, -50%)' : 'translateX(0)';
        instructions.style.opacity = '0.95';
      }
    });
  </script>
  
  <script src="sketch.js"></script>
</body>
</html>
