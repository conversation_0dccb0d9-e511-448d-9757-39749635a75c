{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(python -m http.server 8000)", "Read(/c:\\Users\\<USER>\\Downloads/**)", "Bash(winget install:*)", "<PERSON><PERSON>(dir)", "Bash(git init:*)", "Bash(git add .)", "Bash(git commit -m \"Initial commit: p5.js interactive particle system project\")", "Bash(gh repo create:*)", "Bash(gh auth status)", "Bash(gh api:*)", "Bash(git remote:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(gh auth refresh:*)", "Bash(gh repo view)"], "deny": [], "ask": []}}