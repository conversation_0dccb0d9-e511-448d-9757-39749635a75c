<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>触摸修复测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }
        .test-results {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>触摸修复测试</h3>
        <p>测试内容：</p>
        <ul>
            <li>✅ currentPerformanceLevel 变量已定义</li>
            <li>✅ 触摸事件不再修改全局 mouseX/mouseY</li>
            <li>✅ createTutorialHighlight 函数完整</li>
        </ul>
    </div>
    
    <div class="test-results">
        <h3>测试结果</h3>
        <div id="results">
            <p>🔄 正在加载游戏...</p>
        </div>
    </div>

    <script>
        // 简单的测试脚本来验证修复
        let testResults = [];
        
        function addTestResult(test, passed, message) {
            testResults.push({test, passed, message});
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            let html = '';
            testResults.forEach(result => {
                const icon = result.passed ? '✅' : '❌';
                html += `<p>${icon} ${result.test}: ${result.message}</p>`;
            });
            resultsDiv.innerHTML = html;
        }
        
        // 等待游戏加载完成后运行测试
        setTimeout(() => {
            try {
                // 测试1: 检查 currentPerformanceLevel 是否已定义
                if (typeof currentPerformanceLevel !== 'undefined') {
                    addTestResult('变量定义', true, 'currentPerformanceLevel 已正确定义');
                } else {
                    addTestResult('变量定义', false, 'currentPerformanceLevel 未定义');
                }
                
                // 测试2: 检查 handleMouseClick 函数是否支持参数
                if (typeof handleMouseClick === 'function') {
                    addTestResult('函数改进', true, 'handleMouseClick 函数可用');
                } else {
                    addTestResult('函数改进', false, 'handleMouseClick 函数不可用');
                }
                
                // 测试3: 检查 createTutorialHighlight 函数
                if (typeof createTutorialHighlight === 'function') {
                    addTestResult('教程函数', true, 'createTutorialHighlight 函数完整');
                } else {
                    addTestResult('教程函数', false, 'createTutorialHighlight 函数不完整');
                }
                
            } catch (error) {
                addTestResult('测试执行', false, '测试执行出错: ' + error.message);
            }
        }, 2000);
    </script>
    
    <script src="sketch.js"></script>
</body>
</html>
